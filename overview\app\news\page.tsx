import Image from "next/image";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import {
  ArrowRight,
  Search,
  Calendar,
  Tag,
  Clock,
  Eye,
  MessageCircle,
  Filter,
  TrendingUp
} from "lucide-react";

export default function NewsPage() {
  return (
    <div className="flex flex-col">
      {/* Page Header */}
      <section className="py-16 bg-gradient-to-r from-blue-50 to-green-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <Breadcrumb className="justify-center mb-6">
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink href="/">Home</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbPage>News</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
            <h1 className="text-4xl md:text-6xl font-bold mb-6 text-gray-900">
              Environmental News
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Stay updated with the latest environmental news, conservation updates, 
              and ACEF&apos;s impact stories from across Africa
            </p>
          </div>
        </div>
      </section>

      {/* Search and Filter Section */}
      <section className="py-8 bg-white border-b">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
              <div className="flex items-center space-x-4 w-full md:w-auto">
                <div className="relative flex-1 md:w-80">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input 
                    placeholder="Search news..." 
                    className="pl-10 rounded-full border-gray-300"
                  />
                </div>
                <Button variant="outline" className="rounded-full">
                  <Filter className="h-4 w-4 mr-2" />
                  Filter
                </Button>
              </div>
              <div className="flex items-center space-x-2">
                <Badge variant="outline">All</Badge>
                <Badge variant="outline">Breaking</Badge>
                <Badge variant="outline">Updates</Badge>
                <Badge variant="outline">Achievements</Badge>
                <Badge variant="outline">Partnerships</Badge>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Breaking News */}
      <section className="py-8 bg-red-50 border-b border-red-100">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="flex items-center space-x-3 mb-4">
              <Badge className="bg-red-500 text-white">Breaking News</Badge>
              <TrendingUp className="h-5 w-5 text-red-500" />
              <span className="font-semibold text-red-800">Latest Updates</span>
            </div>
            <div className="bg-white rounded-lg p-6 shadow-lg">
              <h3 className="text-xl font-bold text-gray-900 mb-2">
                ACEF Receives International Recognition for Forest Conservation Efforts
              </h3>
              <p className="text-gray-600 mb-4">
                The Africa Climate and Environment Foundation has been awarded the prestigious 
                Green Africa Award for outstanding contributions to forest conservation...
              </p>
              <div className="flex items-center space-x-4 text-sm text-gray-500">
                <div className="flex items-center space-x-1">
                  <Calendar className="h-4 w-4" />
                  <span>March 20, 2024</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Clock className="h-4 w-4" />
                  <span>2 hours ago</span>
                </div>
                <Link href="/news/acef-receives-international-recognition-for-forest-conservation">
                  <Button variant="link" className="p-0 h-auto text-red-600">
                    Read Full Story
                    <ArrowRight className="ml-1 h-3 w-3" />
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Featured News */}
      <section className="py-12 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="mb-8">
              <Badge className="bg-blue-100 text-blue-800 mb-4">Featured News</Badge>
              <h2 className="text-3xl font-bold text-gray-900">Top Stories</h2>
            </div>
            
            <Card className="border-0 shadow-xl overflow-hidden mb-12">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-0">
                <div className="aspect-video lg:aspect-square relative overflow-hidden">
                  <Image
                    src="https://images.unsplash.com/photo-1569163139394-de4e4f43e4e3?q=80&w=2070&auto=format&fit=crop"
                    alt="ACEF milestone achievement"
                    fill
                    className="object-cover"
                  />
                  <Badge className="absolute top-6 left-6 bg-white text-green-800">Major Achievement</Badge>
                </div>
                
                <div className="p-8 lg:p-12 flex flex-col justify-center">
                  <div className="flex items-center space-x-4 text-sm text-gray-500 mb-4">
                    <div className="flex items-center space-x-1">
                      <Calendar className="h-4 w-4" />
                      <span>March 18, 2024</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Clock className="h-4 w-4" />
                      <span>5 min read</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Tag className="h-4 w-4" />
                      <span>Milestone</span>
                    </div>
                  </div>
                  
                  <h3 className="text-2xl md:text-3xl font-bold mb-6 text-gray-900 leading-tight">
                    ACEF Surpasses 500 Trees Planted Across 15 Communities in Cameroon
                  </h3>
                  
                  <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                    In a remarkable achievement for environmental conservation, ACEF has successfully 
                    planted over 500 trees across 15 communities in Cameroon, marking a significant 
                    milestone in our reforestation efforts and community engagement programs.
                  </p>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <Avatar className="h-12 w-12">
                        <AvatarFallback className="bg-green-100 text-green-800 font-semibold">JD</AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-semibold text-gray-900">Dr. Jane Doe</div>
                        <div className="text-sm text-gray-600">Executive Director, ACEF</div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-1 text-sm text-gray-500">
                        <Eye className="h-4 w-4" />
                        <span>2.1k views</span>
                      </div>
                      <div className="flex items-center space-x-1 text-sm text-gray-500">
                        <MessageCircle className="h-4 w-4" />
                        <span>45 comments</span>
                      </div>
                      <Link href="/news/acef-surpasses-500-trees-planted-milestone">
                        <Button className="bg-green-600 hover:bg-green-700 rounded-full">
                          Read More
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Button>
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </section>

      {/* News Grid */}
      <section className="py-12 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="flex items-center justify-between mb-12">
              <h2 className="text-3xl font-bold text-gray-900">Latest News</h2>
              <Button variant="outline" className="rounded-full">
                View All News
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {/* News Card 1 */}
              <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-white overflow-hidden group">
                <div className="aspect-video relative overflow-hidden">
                  <Image
                    src="https://images.unsplash.com/photo-1558618666-fcd25c85cd64?q=80&w=2070&auto=format&fit=crop"
                    alt="Climate education workshop"
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                  <Badge className="absolute top-4 left-4 bg-blue-100 text-blue-800">Education</Badge>
                </div>
                <CardContent className="p-6">
                  <div className="flex items-center space-x-2 text-sm text-gray-500 mb-3">
                    <Calendar className="h-4 w-4" />
                    <span>March 15, 2024</span>
                    <span>•</span>
                    <Clock className="h-4 w-4" />
                    <span>3 min read</span>
                  </div>
                  <h3 className="text-xl font-bold mb-3 text-gray-900 group-hover:text-green-600 transition-colors">
                    Climate Education Program Reaches 300+ Students in Rural Schools
                  </h3>
                  <p className="text-gray-600 mb-4 leading-relaxed">
                    ACEF&apos;s latest climate education initiative has successfully reached over 300 students
                    across 8 rural schools, teaching environmental conservation...
                  </p>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Avatar className="h-8 w-8">
                        <AvatarFallback className="bg-blue-100 text-blue-800 text-xs">AK</AvatarFallback>
                      </Avatar>
                      <span className="text-sm text-gray-600">Amina Kone</span>
                    </div>
                    <Link href="/news/climate-education-program-reaches-rural-schools">
                      <Button variant="ghost" size="sm" className="text-green-600 hover:text-green-700">
                        Read More
                        <ArrowRight className="ml-1 h-3 w-3" />
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>

              {/* News Card 2 */}
              <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-white overflow-hidden group">
                <div className="aspect-video relative overflow-hidden">
                  <Image
                    src="https://images.unsplash.com/photo-1593113598332-cd288d649433?q=80&w=2070&auto=format&fit=crop"
                    alt="Partnership announcement"
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                  <Badge className="absolute top-4 left-4 bg-green-100 text-green-800">Partnership</Badge>
                </div>
                <CardContent className="p-6">
                  <div className="flex items-center space-x-2 text-sm text-gray-500 mb-3">
                    <Calendar className="h-4 w-4" />
                    <span>March 12, 2024</span>
                    <span>•</span>
                    <Clock className="h-4 w-4" />
                    <span>4 min read</span>
                  </div>
                  <h3 className="text-xl font-bold mb-3 text-gray-900 group-hover:text-green-600 transition-colors">
                    ACEF Partners with Local NGOs for Expanded Conservation Efforts
                  </h3>
                  <p className="text-gray-600 mb-4 leading-relaxed">
                    Strategic partnership announcement with three local environmental organizations 
                    to expand conservation efforts across Central Africa...
                  </p>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Avatar className="h-8 w-8">
                        <AvatarFallback className="bg-green-100 text-green-800 text-xs">MS</AvatarFallback>
                      </Avatar>
                      <span className="text-sm text-gray-600">Michael Smith</span>
                    </div>
                    <Link href="/news/acef-partners-with-local-ngos-for-expanded-conservation">
                      <Button variant="ghost" size="sm" className="text-green-600 hover:text-green-700">
                        Read More
                        <ArrowRight className="ml-1 h-3 w-3" />
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>

              {/* News Card 3 */}
              <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-white overflow-hidden group">
                <div className="aspect-video relative overflow-hidden">
                  <Image
                    src="https://images.unsplash.com/photo-1473773508845-188df298d2d1?q=80&w=2074&auto=format&fit=crop"
                    alt="Sustainable agriculture training"
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                  <Badge className="absolute top-4 left-4 bg-orange-100 text-orange-800">Agriculture</Badge>
                </div>
                <CardContent className="p-6">
                  <div className="flex items-center space-x-2 text-sm text-gray-500 mb-3">
                    <Calendar className="h-4 w-4" />
                    <span>March 10, 2024</span>
                    <span>•</span>
                    <Clock className="h-4 w-4" />
                    <span>6 min read</span>
                  </div>
                  <h3 className="text-xl font-bold mb-3 text-gray-900 group-hover:text-green-600 transition-colors">
                    Sustainable Agriculture Training Benefits 150+ Local Farmers
                  </h3>
                  <p className="text-gray-600 mb-4 leading-relaxed">
                    Comprehensive training program on sustainable farming practices has successfully 
                    trained over 150 farmers in eco-friendly agricultural techniques...
                  </p>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Avatar className="h-8 w-8">
                        <AvatarFallback className="bg-orange-100 text-orange-800 text-xs">PT</AvatarFallback>
                      </Avatar>
                      <span className="text-sm text-gray-600">Paul Tabi</span>
                    </div>
                    <Link href="/news/sustainable-agriculture-training-benefits-farmers">
                      <Button variant="ghost" size="sm" className="text-green-600 hover:text-green-700">
                        Read More
                        <ArrowRight className="ml-1 h-3 w-3" />
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Load More */}
            <div className="text-center mt-12">
              <Button size="lg" variant="outline" className="border-2 border-green-600 text-green-600 hover:bg-green-600 hover:text-white px-8 py-3 rounded-full">
                Load More News
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
