import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Users, Target, Eye, Heart, Award, Calendar } from "lucide-react";

export default function AboutPage() {
  return (
    <div className="flex flex-col">
      {/* Page Header */}
      <section className="py-16 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <nav className="text-sm text-muted-foreground mb-4">
              <span>Home</span> <span className="mx-2">›</span> <span>About Us</span>
            </nav>
            <h1 className="text-4xl md:text-6xl font-bold mb-6">About ACEF</h1>
            <p className="text-xl text-muted-foreground">
              Learn about our mission, vision, and the dedicated team working to protect Africa&apos;s environment
            </p>
          </div>
        </div>
      </section>

      {/* NGO Profile Section */}
      <section className="py-16 bg-background">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <h2 className="text-3xl font-bold mb-6">Africa Climate and Environment Foundation</h2>
                <div className="space-y-4 text-muted-foreground">
                  <p>
                    <strong className="text-foreground">Founded:</strong> March 31st, 2021
                  </p>
                  <p>
                    <strong className="text-foreground">Headquarters:</strong> Limbe, Cameroon
                  </p>
                  <p>
                    <strong className="text-foreground">Type:</strong> International Non-Profit Organization
                  </p>
                </div>
              </div>
              <div className="bg-muted/50 p-8 rounded-lg">
                <h3 className="text-xl font-semibold mb-4 flex items-center">
                  <Target className="mr-2 h-5 w-5 text-primary" />
                  Our Mission
                </h3>
                <p className="text-muted-foreground">
                  To address climate change challenges and promote environmental sustainability across Africa 
                  through community engagement, education, conservation initiatives, and sustainable development programs.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Vision and Values */}
      <section className="py-16 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Eye className="mr-2 h-5 w-5 text-primary" />
                    Our Vision
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">
                    A sustainable and resilient Africa where communities thrive in harmony with nature, 
                    equipped with the knowledge and resources to combat climate change and protect 
                    environmental heritage for future generations.
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Heart className="mr-2 h-5 w-5 text-primary" />
                    Core Values
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-start space-x-2">
                      <Badge variant="outline" className="mt-0.5">1</Badge>
                      <div>
                        <strong>Sustainability:</strong> Long-term environmental solutions
                      </div>
                    </div>
                    <div className="flex items-start space-x-2">
                      <Badge variant="outline" className="mt-0.5">2</Badge>
                      <div>
                        <strong>Community:</strong> Grassroots engagement and empowerment
                      </div>
                    </div>
                    <div className="flex items-start space-x-2">
                      <Badge variant="outline" className="mt-0.5">3</Badge>
                      <div>
                        <strong>Innovation:</strong> Creative approaches to environmental challenges
                      </div>
                    </div>
                    <div className="flex items-start space-x-2">
                      <Badge variant="outline" className="mt-0.5">4</Badge>
                      <div>
                        <strong>Collaboration:</strong> Partnerships for greater impact
                      </div>
                    </div>
                    <div className="flex items-start space-x-2">
                      <Badge variant="outline" className="mt-0.5">5</Badge>
                      <div>
                        <strong>Transparency:</strong> Open and accountable operations
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Our History Section */}
      <section className="py-16 bg-background">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-center mb-12">Our Journey</h2>
            <div className="space-y-8">
              <div className="flex items-start space-x-4">
                <div className="bg-primary text-primary-foreground rounded-full p-2 mt-1">
                  <Calendar className="h-4 w-4" />
                </div>
                <div>
                  <h3 className="font-semibold text-lg">2021 - Foundation</h3>
                  <p className="text-muted-foreground">
                    ACEF was officially founded on March 31st, 2021, in Limbe, Cameroon, with a vision 
                    to address pressing environmental challenges across Africa.
                  </p>
                </div>
              </div>
              
              <Separator />
              
              <div className="flex items-start space-x-4">
                <div className="bg-primary text-primary-foreground rounded-full p-2 mt-1">
                  <Users className="h-4 w-4" />
                </div>
                <div>
                  <h3 className="font-semibold text-lg">2022 - Community Engagement</h3>
                  <p className="text-muted-foreground">
                    Launched our first community-based environmental education programs, reaching over 
                    500 individuals across rural communities in Cameroon.
                  </p>
                </div>
              </div>
              
              <Separator />
              
              <div className="flex items-start space-x-4">
                <div className="bg-primary text-primary-foreground rounded-full p-2 mt-1">
                  <Award className="h-4 w-4" />
                </div>
                <div>
                  <h3 className="font-semibold text-lg">2023 - Program Expansion</h3>
                  <p className="text-muted-foreground">
                    Expanded operations to include forest restoration projects and sustainable agriculture 
                    initiatives, partnering with local organizations and government agencies.
                  </p>
                </div>
              </div>
              
              <Separator />
              
              <div className="flex items-start space-x-4">
                <div className="bg-primary text-primary-foreground rounded-full p-2 mt-1">
                  <Target className="h-4 w-4" />
                </div>
                <div>
                  <h3 className="font-semibold text-lg">2024 - Impact Scaling</h3>
                  <p className="text-muted-foreground">
                    Achieved significant milestones with over 500 trees planted, 1,200+ people reached 
                    through our programs, and partnerships established across 15+ communities.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Our Team Section */}
      <section className="py-16 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl font-bold text-center mb-12">Meet Our Team</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              <Card>
                <CardHeader className="text-center">
                  <div className="w-20 h-20 bg-primary rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-primary-foreground font-bold text-xl">JD</span>
                  </div>
                  <CardTitle>Dr. Jane Doe</CardTitle>
                  <CardDescription>Executive Director</CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground text-center">
                    Environmental scientist with 15+ years experience in climate change research and policy.
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="text-center">
                  <div className="w-20 h-20 bg-primary rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-primary-foreground font-bold text-xl">MS</span>
                  </div>
                  <CardTitle>Michael Smith</CardTitle>
                  <CardDescription>Program Manager</CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground text-center">
                    Community development specialist focused on sustainable agriculture and rural empowerment.
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="text-center">
                  <div className="w-20 h-20 bg-primary rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-primary-foreground font-bold text-xl">AK</span>
                  </div>
                  <CardTitle>Amina Kone</CardTitle>
                  <CardDescription>Education Coordinator</CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground text-center">
                    Environmental educator passionate about climate awareness and community engagement.
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="text-center">
                  <div className="w-20 h-20 bg-primary rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-primary-foreground font-bold text-xl">PT</span>
                  </div>
                  <CardTitle>Paul Tabi</CardTitle>
                  <CardDescription>Field Operations Lead</CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground text-center">
                    Local community leader with expertise in forest conservation and restoration projects.
                  </p>
                </CardContent>
              </Card>
            </div>
            <div className="text-center mt-8">
              <Button variant="outline">
                Meet the Full Team
                <Users className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Impact Numbers Section */}
      <section className="py-16 bg-primary text-primary-foreground">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-12">Our Impact in Numbers</h2>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              <div>
                <div className="text-4xl font-bold mb-2">500+</div>
                <div className="text-lg opacity-90">Trees Planted</div>
              </div>
              <div>
                <div className="text-4xl font-bold mb-2">1,200+</div>
                <div className="text-lg opacity-90">People Reached</div>
              </div>
              <div>
                <div className="text-4xl font-bold mb-2">15+</div>
                <div className="text-lg opacity-90">Communities Served</div>
              </div>
              <div>
                <div className="text-4xl font-bold mb-2">3+</div>
                <div className="text-lg opacity-90">Years of Service</div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
