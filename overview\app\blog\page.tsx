import Image from "next/image";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import {
  ArrowRight,
  Search,
  Calendar,
  User,
  Tag,
  Clock,
  Eye,
  MessageCircle,
  Filter
} from "lucide-react";

export default function BlogPage() {
  return (
    <div className="flex flex-col">
      {/* Page Header */}
      <section className="py-16 bg-gradient-to-r from-green-50 to-blue-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <Breadcrumb className="justify-center mb-6">
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink href="/">Home</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbPage>Blog & News</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
            <h1 className="text-4xl md:text-6xl font-bold mb-6 text-gray-900">
              Environmental Blog
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Explore in-depth articles, insights, and stories about environmental conservation,
              climate action, and sustainable development in Africa
            </p>
          </div>
        </div>
      </section>

      {/* Search and Filter Section */}
      <section className="py-8 bg-white border-b">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
              <div className="flex items-center space-x-4 w-full md:w-auto">
                <div className="relative flex-1 md:w-80">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input 
                    placeholder="Search articles..." 
                    className="pl-10 rounded-full border-gray-300"
                  />
                </div>
                <Button variant="outline" className="rounded-full">
                  <Filter className="h-4 w-4 mr-2" />
                  Filter
                </Button>
              </div>
              <div className="flex items-center space-x-2">
                <Badge variant="outline">All</Badge>
                <Badge variant="outline">Conservation</Badge>
                <Badge variant="outline">Climate Action</Badge>
                <Badge variant="outline">Community Stories</Badge>
                <Badge variant="outline">Insights</Badge>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Article */}
      <section className="py-12 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="mb-8">
              <Badge className="bg-purple-100 text-purple-800 mb-4">Featured Article</Badge>
            </div>

            <Card className="border-0 shadow-2xl overflow-hidden">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-0">
                <div className="aspect-video lg:aspect-square relative overflow-hidden">
                  <Image
                    src="https://images.unsplash.com/photo-1569163139394-de4e4f43e4e3?q=80&w=2070&auto=format&fit=crop"
                    alt="Environmental conservation in Africa"
                    fill
                    className="object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                  <Badge className="absolute top-6 left-6 bg-white text-purple-800">In-Depth Analysis</Badge>
                </div>
                
                <div className="p-8 lg:p-12 flex flex-col justify-center">
                  <div className="flex items-center space-x-4 text-sm text-gray-500 mb-4">
                    <div className="flex items-center space-x-1">
                      <Calendar className="h-4 w-4" />
                      <span>March 15, 2024</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Clock className="h-4 w-4" />
                      <span>5 min read</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Tag className="h-4 w-4" />
                      <span>Conservation</span>
                    </div>
                  </div>
                  
                  <h2 className="text-3xl md:text-4xl font-bold mb-6 text-gray-900 leading-tight">
                    The Future of Environmental Conservation in Africa: Lessons from Community-Led Initiatives
                  </h2>

                  <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                    As Africa faces unprecedented environmental challenges, community-led conservation
                    initiatives are emerging as powerful catalysts for change. This comprehensive analysis
                    explores how grassroots movements are reshaping environmental protection across the
                    continent and what this means for the future of conservation.
                  </p>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <Avatar className="h-12 w-12">
                        <AvatarFallback className="bg-green-100 text-green-800 font-semibold">JD</AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-semibold text-gray-900">Dr. Jane Doe</div>
                        <div className="text-sm text-gray-600">Executive Director, ACEF</div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-1 text-sm text-gray-500">
                        <Eye className="h-4 w-4" />
                        <span>1.2k views</span>
                      </div>
                      <div className="flex items-center space-x-1 text-sm text-gray-500">
                        <MessageCircle className="h-4 w-4" />
                        <span>24 comments</span>
                      </div>
                      <Link href="/blog/future-of-environmental-conservation-in-africa">
                        <Button className="bg-green-600 hover:bg-green-700 rounded-full">
                          Read Full Article
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Button>
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </section>

      {/* Articles Grid */}
      <section className="py-12 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="flex items-center justify-between mb-12">
              <h2 className="text-3xl font-bold text-gray-900">Latest Articles</h2>
              <Button variant="outline" className="rounded-full">
                View All Articles
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {/* Article Card 1 */}
              <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-white overflow-hidden group">
                <div className="aspect-video relative overflow-hidden">
                  <Image
                    src="https://images.unsplash.com/photo-1558618666-fcd25c85cd64?q=80&w=2070&auto=format&fit=crop"
                    alt="Climate education and awareness"
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                  <Badge className="absolute top-4 left-4 bg-blue-100 text-blue-800">Education</Badge>
                </div>
                <CardContent className="p-6">
                  <div className="flex items-center space-x-2 text-sm text-gray-500 mb-3">
                    <Calendar className="h-4 w-4" />
                    <span>March 10, 2024</span>
                    <span>•</span>
                    <Clock className="h-4 w-4" />
                    <span>3 min read</span>
                  </div>
                  <h3 className="text-xl font-bold mb-3 text-gray-900 group-hover:text-green-600 transition-colors">
                    Building Climate Resilience Through Education: A Community Approach
                  </h3>
                  <p className="text-gray-600 mb-4 leading-relaxed">
                    Exploring how educational initiatives can empower communities to build resilience
                    against climate change impacts while fostering environmental stewardship...
                  </p>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Avatar className="h-8 w-8">
                        <AvatarFallback className="bg-blue-100 text-blue-800 text-xs">AK</AvatarFallback>
                      </Avatar>
                      <span className="text-sm text-gray-600">Amina Kone</span>
                    </div>
                    <Link href="/blog/building-climate-resilience-through-education">
                      <Button variant="ghost" size="sm" className="text-green-600 hover:text-green-700">
                        Read More
                        <ArrowRight className="ml-1 h-3 w-3" />
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>

              {/* Article Card 2 */}
              <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-white overflow-hidden group">
                <div className="aspect-video bg-gradient-to-br from-green-400 to-green-600 relative overflow-hidden">
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-center text-white">
                      <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-2">
                        <User className="h-8 w-8" />
                      </div>
                      <p className="font-medium">Partnership</p>
                    </div>
                  </div>
                  <Badge className="absolute top-4 left-4 bg-green-100 text-green-800">Partnership</Badge>
                </div>
                <CardContent className="p-6">
                  <div className="flex items-center space-x-2 text-sm text-gray-500 mb-3">
                    <Calendar className="h-4 w-4" />
                    <span>March 5, 2024</span>
                    <span>•</span>
                    <Clock className="h-4 w-4" />
                    <span>4 min read</span>
                  </div>
                  <h3 className="text-xl font-bold mb-3 text-gray-900 group-hover:text-green-600 transition-colors">
                    New Partnership Expands Conservation Efforts
                  </h3>
                  <p className="text-gray-600 mb-4 leading-relaxed">
                    ACEF announces strategic partnership with local environmental groups to expand 
                    conservation efforts across Central Africa...
                  </p>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Avatar className="h-8 w-8">
                        <AvatarFallback className="bg-green-100 text-green-800 text-xs">MS</AvatarFallback>
                      </Avatar>
                      <span className="text-sm text-gray-600">Michael Smith</span>
                    </div>
                    <Button variant="ghost" size="sm" className="text-green-600 hover:text-green-700">
                      Read More
                      <ArrowRight className="ml-1 h-3 w-3" />
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Article Card 3 */}
              <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-white overflow-hidden group">
                <div className="aspect-video bg-gradient-to-br from-yellow-400 to-orange-500 relative overflow-hidden">
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-center text-white">
                      <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-2">
                        <Tag className="h-8 w-8" />
                      </div>
                      <p className="font-medium">Agriculture</p>
                    </div>
                  </div>
                  <Badge className="absolute top-4 left-4 bg-orange-100 text-orange-800">Agriculture</Badge>
                </div>
                <CardContent className="p-6">
                  <div className="flex items-center space-x-2 text-sm text-gray-500 mb-3">
                    <Calendar className="h-4 w-4" />
                    <span>February 28, 2024</span>
                    <span>•</span>
                    <Clock className="h-4 w-4" />
                    <span>6 min read</span>
                  </div>
                  <h3 className="text-xl font-bold mb-3 text-gray-900 group-hover:text-green-600 transition-colors">
                    Sustainable Agriculture Training Program Launch
                  </h3>
                  <p className="text-gray-600 mb-4 leading-relaxed">
                    New comprehensive training program helps local farmers adopt eco-friendly 
                    agricultural practices and improve food security...
                  </p>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Avatar className="h-8 w-8">
                        <AvatarFallback className="bg-orange-100 text-orange-800 text-xs">PT</AvatarFallback>
                      </Avatar>
                      <span className="text-sm text-gray-600">Paul Tabi</span>
                    </div>
                    <Button variant="ghost" size="sm" className="text-green-600 hover:text-green-700">
                      Read More
                      <ArrowRight className="ml-1 h-3 w-3" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Load More */}
            <div className="text-center mt-12">
              <Button size="lg" variant="outline" className="border-2 border-green-600 text-green-600 hover:bg-green-600 hover:text-white px-8 py-3 rounded-full">
                Load More Articles
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
