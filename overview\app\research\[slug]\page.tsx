import Image from "next/image";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  ArrowRight, 
  Calendar, 
  Download, 
  FileText, 
  User, 
  Tag, 
  Eye,
  Quote,
  Share2,
  ArrowLeft,
  BookOpen,
  Award,
  ExternalLink
} from "lucide-react";

interface ResearchArticlePageProps {
  params: {
    slug: string;
  };
}

export default function ResearchArticlePage({ params }: ResearchArticlePageProps) {
  // In a real app, you would fetch the research article data based on the slug
  const researchArticle = {
    title: "Climate Change Adaptation Strategies for Smallholder Farmers in Cameroon: A Comprehensive Analysis",
    abstract: "This comprehensive study examines the climate change adaptation strategies employed by smallholder farmers in Cameroon's agricultural regions. Through extensive field research and data analysis, we identify key challenges and propose sustainable solutions for climate-resilient agriculture in Central Africa.",
    authors: [
      { name: "Dr. Jane Doe", affiliation: "ACEF, Environmental Research Division", avatar: "JD" },
      { name: "Michael Smith", affiliation: "ACEF, Program Management", avatar: "MS" },
      { name: "Dr. Paul Tabi", affiliation: "University of Buea, Agricultural Sciences", avatar: "PT" }
    ],
    publishedAt: "March 2024",
    journal: "African Environmental Research",
    volume: "15",
    issue: "3",
    pages: "45-72",
    doi: "10.1234/aer.2024.003",
    keywords: ["Climate Change", "Agriculture", "Adaptation", "Cameroon", "Sustainability", "Smallholder Farmers"],
    views: 2341,
    downloads: 456,
    citations: 23,
    fileSize: "2.4 MB",
    pageCount: 28,
    language: "English",
    peerReviewed: true,
    openAccess: true,
    category: "Research Paper",
    methodology: "Mixed Methods",
    studyPeriod: "2022-2023",
    sampleSize: "450 farmers across 15 communities"
  };

  return (
    <div className="flex flex-col">
      {/* Breadcrumb */}
      <section className="py-6 bg-gray-50 border-b">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink href="/">Home</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbLink href="/research">Research</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbPage>{researchArticle.title.substring(0, 50)}...</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </div>
      </section>

      {/* Article Header */}
      <section className="py-12 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <Button variant="ghost" className="mb-6 p-0 h-auto text-gray-600 hover:text-green-600">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Research
            </Button>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
              {/* Main Content */}
              <div className="lg:col-span-2">
                <div className="mb-8">
                  <div className="flex items-center space-x-3 mb-4">
                    <Badge className="bg-green-100 text-green-800">{researchArticle.category}</Badge>
                    {researchArticle.peerReviewed && (
                      <Badge variant="outline" className="border-green-500 text-green-700">
                        <Award className="h-3 w-3 mr-1" />
                        Peer Reviewed
                      </Badge>
                    )}
                    {researchArticle.openAccess && (
                      <Badge className="bg-blue-100 text-blue-800">Open Access</Badge>
                    )}
                  </div>
                  
                  <h1 className="text-3xl md:text-4xl font-bold mb-6 text-gray-900 leading-tight">
                    {researchArticle.title}
                  </h1>

                  {/* Authors */}
                  <div className="space-y-3 mb-8">
                    {researchArticle.authors.map((author, index) => (
                      <div key={index} className="flex items-center space-x-3">
                        <Avatar className="h-10 w-10">
                          <AvatarFallback className="bg-green-100 text-green-800 font-semibold">
                            {author.avatar}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-semibold text-gray-900">{author.name}</div>
                          <div className="text-sm text-gray-600">{author.affiliation}</div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Publication Info */}
                  <div className="flex flex-wrap items-center gap-6 text-sm text-gray-500 mb-8">
                    <div className="flex items-center space-x-1">
                      <Calendar className="h-4 w-4" />
                      <span>Published {researchArticle.publishedAt}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <BookOpen className="h-4 w-4" />
                      <span>{researchArticle.journal}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <FileText className="h-4 w-4" />
                      <span>{researchArticle.pageCount} pages</span>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex flex-wrap gap-4 mb-12">
                    <Button className="bg-green-600 hover:bg-green-700">
                      <Download className="h-4 w-4 mr-2" />
                      Download PDF ({researchArticle.fileSize})
                    </Button>
                    <Button variant="outline">
                      <Eye className="h-4 w-4 mr-2" />
                      View Online
                    </Button>
                    <Button variant="outline">
                      <Quote className="h-4 w-4 mr-2" />
                      Cite Article
                    </Button>
                    <Button variant="outline">
                      <Share2 className="h-4 w-4 mr-2" />
                      Share
                    </Button>
                  </div>
                </div>

                {/* Tabs for different sections */}
                <Tabs defaultValue="abstract" className="w-full">
                  <TabsList className="grid w-full grid-cols-4">
                    <TabsTrigger value="abstract">Abstract</TabsTrigger>
                    <TabsTrigger value="methodology">Methodology</TabsTrigger>
                    <TabsTrigger value="findings">Key Findings</TabsTrigger>
                    <TabsTrigger value="references">References</TabsTrigger>
                  </TabsList>

                  <TabsContent value="abstract" className="mt-8">
                    <Card className="border-0 shadow-lg">
                      <CardHeader>
                        <CardTitle>Abstract</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-gray-700 leading-relaxed mb-6">
                          {researchArticle.abstract}
                        </p>
                        <p className="text-gray-700 leading-relaxed mb-6">
                          This study employed a mixed-methods approach, combining quantitative surveys with 
                          qualitative interviews to gather comprehensive data from 450 smallholder farmers 
                          across 15 communities in Cameroon's major agricultural regions. The research 
                          identified several key adaptation strategies currently being employed by farmers, 
                          including crop diversification, water conservation techniques, and soil management practices.
                        </p>
                        <p className="text-gray-700 leading-relaxed">
                          Our findings reveal that while farmers are actively adapting to climate change, 
                          they face significant barriers including limited access to climate information, 
                          inadequate financial resources, and insufficient technical support. The study 
                          proposes a framework for enhancing climate resilience through improved extension 
                          services, community-based adaptation programs, and policy interventions that 
                          support smallholder farmers in their adaptation efforts.
                        </p>
                      </CardContent>
                    </Card>
                  </TabsContent>

                  <TabsContent value="methodology" className="mt-8">
                    <Card className="border-0 shadow-lg">
                      <CardHeader>
                        <CardTitle>Research Methodology</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-6">
                          <div>
                            <h3 className="font-semibold text-gray-900 mb-2">Study Design</h3>
                            <p className="text-gray-700">
                              This research employed a mixed-methods approach combining quantitative 
                              surveys and qualitative interviews to provide comprehensive insights 
                              into climate adaptation strategies.
                            </p>
                          </div>
                          <div>
                            <h3 className="font-semibold text-gray-900 mb-2">Sample Selection</h3>
                            <p className="text-gray-700">
                              A total of 450 smallholder farmers were selected using stratified 
                              random sampling across 15 communities in three major agricultural 
                              regions of Cameroon.
                            </p>
                          </div>
                          <div>
                            <h3 className="font-semibold text-gray-900 mb-2">Data Collection</h3>
                            <p className="text-gray-700">
                              Data collection was conducted over 18 months (2022-2023) using 
                              structured questionnaires, focus group discussions, and in-depth 
                              interviews with key informants.
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>

                  <TabsContent value="findings" className="mt-8">
                    <Card className="border-0 shadow-lg">
                      <CardHeader>
                        <CardTitle>Key Findings</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-6">
                          <div>
                            <h3 className="font-semibold text-gray-900 mb-2">Adaptation Strategies</h3>
                            <ul className="list-disc list-inside text-gray-700 space-y-1">
                              <li>85% of farmers have adopted crop diversification practices</li>
                              <li>67% implement water conservation techniques</li>
                              <li>54% use improved soil management practices</li>
                              <li>43% have shifted planting seasons</li>
                            </ul>
                          </div>
                          <div>
                            <h3 className="font-semibold text-gray-900 mb-2">Major Challenges</h3>
                            <ul className="list-disc list-inside text-gray-700 space-y-1">
                              <li>Limited access to climate information (78% of respondents)</li>
                              <li>Inadequate financial resources (71% of respondents)</li>
                              <li>Insufficient technical support (65% of respondents)</li>
                              <li>Market access constraints (58% of respondents)</li>
                            </ul>
                          </div>
                          <div>
                            <h3 className="font-semibold text-gray-900 mb-2">Recommendations</h3>
                            <ul className="list-disc list-inside text-gray-700 space-y-1">
                              <li>Strengthen climate information systems</li>
                              <li>Improve access to climate-smart technologies</li>
                              <li>Enhance extension services</li>
                              <li>Develop community-based adaptation programs</li>
                            </ul>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>

                  <TabsContent value="references" className="mt-8">
                    <Card className="border-0 shadow-lg">
                      <CardHeader>
                        <CardTitle>References</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4 text-sm text-gray-700">
                          <p>1. Adesina, A. A. (2020). Climate change adaptation in African agriculture: A systematic review. <em>African Journal of Agricultural Research</em>, 15(8), 1123-1135.</p>
                          <p>2. Boko, M., et al. (2019). Africa climate change 2019: Impacts, adaptation and vulnerability. <em>Cambridge University Press</em>.</p>
                          <p>3. Challinor, A. J., et al. (2018). A meta-analysis of crop yield under climate change and adaptation. <em>Nature Climate Change</em>, 4(4), 287-291.</p>
                          <p>4. Deressa, T. T., et al. (2021). Determinants of farmers' choice of adaptation methods to climate change in the Nile Basin of Ethiopia. <em>Global Environmental Change</em>, 19(2), 248-255.</p>
                          <p>5. Fosu-Mensah, B. Y., et al. (2020). Farmers' perception and adaptation to climate change: A case study of Sekyedumase district in Ghana. <em>Environment, Development and Sustainability</em>, 14(4), 495-505.</p>
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>
                </Tabs>
              </div>

              {/* Sidebar */}
              <div className="lg:col-span-1">
                <div className="space-y-6">
                  {/* Article Metrics */}
                  <Card className="border-0 shadow-lg">
                    <CardHeader>
                      <CardTitle className="text-lg">Article Metrics</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <span className="text-gray-600">Views</span>
                          <span className="font-semibold">{researchArticle.views.toLocaleString()}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-gray-600">Downloads</span>
                          <span className="font-semibold">{researchArticle.downloads}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-gray-600">Citations</span>
                          <span className="font-semibold">{researchArticle.citations}</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Publication Details */}
                  <Card className="border-0 shadow-lg">
                    <CardHeader>
                      <CardTitle className="text-lg">Publication Details</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3 text-sm">
                        <div>
                          <span className="font-medium text-gray-900">Journal:</span>
                          <p className="text-gray-600">{researchArticle.journal}</p>
                        </div>
                        <div>
                          <span className="font-medium text-gray-900">Volume & Issue:</span>
                          <p className="text-gray-600">Vol. {researchArticle.volume}, Issue {researchArticle.issue}</p>
                        </div>
                        <div>
                          <span className="font-medium text-gray-900">Pages:</span>
                          <p className="text-gray-600">{researchArticle.pages}</p>
                        </div>
                        <div>
                          <span className="font-medium text-gray-900">DOI:</span>
                          <p className="text-gray-600 break-all">{researchArticle.doi}</p>
                        </div>
                        <div>
                          <span className="font-medium text-gray-900">Language:</span>
                          <p className="text-gray-600">{researchArticle.language}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Keywords */}
                  <Card className="border-0 shadow-lg">
                    <CardHeader>
                      <CardTitle className="text-lg">Keywords</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex flex-wrap gap-2">
                        {researchArticle.keywords.map((keyword, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {keyword}
                          </Badge>
                        ))}
                      </div>
                    </CardContent>
                  </Card>

                  {/* Study Information */}
                  <Card className="border-0 shadow-lg">
                    <CardHeader>
                      <CardTitle className="text-lg">Study Information</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3 text-sm">
                        <div>
                          <span className="font-medium text-gray-900">Methodology:</span>
                          <p className="text-gray-600">{researchArticle.methodology}</p>
                        </div>
                        <div>
                          <span className="font-medium text-gray-900">Study Period:</span>
                          <p className="text-gray-600">{researchArticle.studyPeriod}</p>
                        </div>
                        <div>
                          <span className="font-medium text-gray-900">Sample Size:</span>
                          <p className="text-gray-600">{researchArticle.sampleSize}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Related Articles */}
                  <Card className="border-0 shadow-lg">
                    <CardHeader>
                      <CardTitle className="text-lg">Related Research</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium text-gray-900 text-sm mb-1">
                            Forest Conservation Policies in Central Africa
                          </h4>
                          <p className="text-xs text-gray-600 mb-2">Policy Brief • February 2024</p>
                          <Button variant="ghost" size="sm" className="p-0 h-auto text-green-600 text-xs">
                            View Article
                            <ExternalLink className="ml-1 h-3 w-3" />
                          </Button>
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900 text-sm mb-1">
                            Community-Based Environmental Education Impact
                          </h4>
                          <p className="text-xs text-gray-600 mb-2">Research Paper • January 2024</p>
                          <Button variant="ghost" size="sm" className="p-0 h-auto text-green-600 text-xs">
                            View Article
                            <ExternalLink className="ml-1 h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
