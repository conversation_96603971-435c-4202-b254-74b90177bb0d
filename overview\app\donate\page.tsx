import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import { 
  ArrowRight, 
  Heart, 
  Shield, 
  CreditCard, 
  DollarSign,
  TreePine,
  Users,
  Globe,
  CheckCircle
} from "lucide-react";

export default function DonatePage() {
  return (
    <div className="flex flex-col">
      {/* Page Header */}
      <section className="py-16 bg-gradient-to-r from-green-50 to-blue-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <Breadcrumb className="justify-center mb-6">
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink href="/">Home</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbPage>Donate</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
            <h1 className="text-4xl md:text-6xl font-bold mb-6 text-gray-900">
              Support Environmental Conservation
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Your donation helps us protect Africa&apos;s environment, plant trees, educate communities,
              and create a sustainable future for generations to come.
            </p>
          </div>
        </div>
      </section>

      {/* Donation Impact */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold mb-6 text-gray-900">
                Your Impact
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                See how your donation directly contributes to environmental conservation efforts
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
              <Card className="border-0 shadow-lg text-center hover:shadow-xl transition-all">
                <CardContent className="p-8">
                  <div className="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6">
                    <TreePine className="h-8 w-8 text-green-600" />
                  </div>
                  <h3 className="text-2xl font-bold text-green-600 mb-2">$25</h3>
                  <h4 className="text-lg font-semibold text-gray-900 mb-4">Plant 5 Trees</h4>
                  <p className="text-gray-600">
                    Funds the planting and care of 5 native trees, contributing to reforestation efforts
                  </p>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-lg text-center hover:shadow-xl transition-all">
                <CardContent className="p-8">
                  <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6">
                    <Users className="h-8 w-8 text-blue-600" />
                  </div>
                  <h3 className="text-2xl font-bold text-blue-600 mb-2">$50</h3>
                  <h4 className="text-lg font-semibold text-gray-900 mb-4">Train 10 People</h4>
                  <p className="text-gray-600">
                    Provides environmental education and training for 10 community members
                  </p>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-lg text-center hover:shadow-xl transition-all">
                <CardContent className="p-8">
                  <div className="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6">
                    <Globe className="h-8 w-8 text-purple-600" />
                  </div>
                  <h3 className="text-2xl font-bold text-purple-600 mb-2">$100</h3>
                  <h4 className="text-lg font-semibold text-gray-900 mb-4">Support a Community</h4>
                  <p className="text-gray-600">
                    Funds a complete environmental program for one community for a month
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Donation Form */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
              {/* Donation Amounts */}
              <div>
                <h2 className="text-3xl font-bold mb-8 text-gray-900">Make a Donation</h2>
                
                <Card className="border-0 shadow-xl">
                  <CardContent className="p-8">
                    <h3 className="text-xl font-semibold mb-6 text-gray-900">Choose Amount</h3>
                    
                    <div className="grid grid-cols-2 gap-4 mb-6">
                      <Button variant="outline" className="h-16 text-lg font-semibold border-2 hover:border-green-600 hover:text-green-600">
                        $25
                      </Button>
                      <Button variant="outline" className="h-16 text-lg font-semibold border-2 hover:border-green-600 hover:text-green-600">
                        $50
                      </Button>
                      <Button variant="outline" className="h-16 text-lg font-semibold border-2 hover:border-green-600 hover:text-green-600">
                        $100
                      </Button>
                      <Button variant="outline" className="h-16 text-lg font-semibold border-2 hover:border-green-600 hover:text-green-600">
                        $250
                      </Button>
                    </div>
                    
                    <div className="mb-6">
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Custom Amount
                      </label>
                      <div className="relative">
                        <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                        <Input 
                          placeholder="Enter amount" 
                          className="pl-10 h-12 text-lg"
                        />
                      </div>
                    </div>
                    
                    <div className="mb-6">
                      <h4 className="font-semibold text-gray-900 mb-3">Donation Type</h4>
                      <div className="space-y-2">
                        <label className="flex items-center space-x-3">
                          <input type="radio" name="donation-type" className="text-green-600" defaultChecked />
                          <span>One-time donation</span>
                        </label>
                        <label className="flex items-center space-x-3">
                          <input type="radio" name="donation-type" className="text-green-600" />
                          <span>Monthly recurring</span>
                        </label>
                      </div>
                    </div>
                    
                    <Button className="w-full bg-green-600 hover:bg-green-700 h-12 text-lg rounded-full">
                      <Heart className="mr-2 h-5 w-5" />
                      Donate Now
                    </Button>
                  </CardContent>
                </Card>
              </div>

              {/* Why Donate */}
              <div>
                <h2 className="text-3xl font-bold mb-8 text-gray-900">Why Your Donation Matters</h2>
                
                <div className="space-y-6">
                  <div className="flex items-start space-x-4">
                    <div className="bg-green-100 p-2 rounded-lg">
                      <CheckCircle className="h-6 w-6 text-green-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-2">Direct Environmental Impact</h3>
                      <p className="text-gray-600">
                        100% of your donation goes directly to environmental conservation projects, 
                        tree planting, and community education programs.
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-4">
                    <div className="bg-blue-100 p-2 rounded-lg">
                      <Shield className="h-6 w-6 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-2">Transparent Use of Funds</h3>
                      <p className="text-gray-600">
                        We provide detailed reports on how your donation is used and the impact 
                        it creates in local communities.
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-4">
                    <div className="bg-purple-100 p-2 rounded-lg">
                      <Globe className="h-6 w-6 text-purple-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-2">Long-term Sustainability</h3>
                      <p className="text-gray-600">
                        Your support helps build sustainable environmental programs that continue 
                        to benefit communities for years to come.
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-4">
                    <div className="bg-orange-100 p-2 rounded-lg">
                      <CreditCard className="h-6 w-6 text-orange-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-2">Secure & Easy</h3>
                      <p className="text-gray-600">
                        Our donation platform uses bank-level security to ensure your information 
                        is safe and your donation process is smooth.
                      </p>
                    </div>
                  </div>
                </div>
                
                <Card className="mt-8 border-0 shadow-lg bg-green-50">
                  <CardContent className="p-6">
                    <h3 className="font-semibold text-green-800 mb-3">Tax Deductible</h3>
                    <p className="text-green-700 text-sm">
                      ACEF is a registered non-profit organization. Your donation may be tax-deductible. 
                      You will receive a receipt for your records.
                    </p>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Other Ways to Help */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold mb-4 text-gray-900">Other Ways to Help</h2>
              <p className="text-gray-600">
                Can&apos;t donate right now? There are other ways you can support our mission
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <Card className="border-0 shadow-lg text-center hover:shadow-xl transition-all">
                <CardContent className="p-8">
                  <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6">
                    <Users className="h-8 w-8 text-blue-600" />
                  </div>
                  <h3 className="text-xl font-bold mb-4 text-gray-900">Volunteer</h3>
                  <p className="text-gray-600 mb-6">
                    Join our team of volunteers and contribute your time and skills to environmental conservation
                  </p>
                  <Link href="/volunteer">
                    <Button variant="outline" className="rounded-full">
                      Learn More
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </Link>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-lg text-center hover:shadow-xl transition-all">
                <CardContent className="p-8">
                  <div className="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6">
                    <Heart className="h-8 w-8 text-green-600" />
                  </div>
                  <h3 className="text-xl font-bold mb-4 text-gray-900">Spread Awareness</h3>
                  <p className="text-gray-600 mb-6">
                    Share our mission with friends and family to help raise awareness about environmental issues
                  </p>
                  <Button variant="outline" className="rounded-full">
                    Share Now
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-lg text-center hover:shadow-xl transition-all">
                <CardContent className="p-8">
                  <div className="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6">
                    <Globe className="h-8 w-8 text-purple-600" />
                  </div>
                  <h3 className="text-xl font-bold mb-4 text-gray-900">Partner with Us</h3>
                  <p className="text-gray-600 mb-6">
                    Explore partnership opportunities for organizations looking to support environmental causes
                  </p>
                  <Link href="/contact">
                    <Button variant="outline" className="rounded-full">
                      Contact Us
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
