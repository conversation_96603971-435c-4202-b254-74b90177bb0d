import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  ArrowRight, 
  Heart, 
  Users, 
  Clock, 
  MapPin, 
  CheckCircle,
  Star,
  Quote,
  TreePine,
  Lightbulb,
  Camera,
  Megaphone
} from "lucide-react";

export default function VolunteerPage() {
  return (
    <div className="flex flex-col">
      {/* Page Header */}
      <section className="py-16 bg-gradient-to-r from-green-50 to-blue-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <Breadcrumb className="justify-center mb-6">
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink href="/">Home</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbPage>Volunteer</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
            <h1 className="text-4xl md:text-6xl font-bold mb-6 text-gray-900">
              Volunteer With ACEF
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Join our community of passionate volunteers working to protect Africa's environment 
              and create a sustainable future for generations to come
            </p>
          </div>
        </div>
      </section>

      {/* Why Volunteer Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold mb-6 text-gray-900">
                Why Volunteer With Us?
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Make a real difference in environmental conservation while gaining valuable experience 
                and connecting with like-minded individuals
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <Card className="border-0 shadow-lg text-center">
                <CardContent className="p-8">
                  <div className="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6">
                    <Heart className="h-8 w-8 text-green-600" />
                  </div>
                  <h3 className="text-xl font-bold mb-4 text-gray-900">Make an Impact</h3>
                  <p className="text-gray-600">
                    Contribute directly to environmental conservation efforts and see the tangible 
                    results of your work in local communities.
                  </p>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-lg text-center">
                <CardContent className="p-8">
                  <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6">
                    <Users className="h-8 w-8 text-blue-600" />
                  </div>
                  <h3 className="text-xl font-bold mb-4 text-gray-900">Build Community</h3>
                  <p className="text-gray-600">
                    Connect with passionate individuals who share your commitment to environmental 
                    protection and sustainable development.
                  </p>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-lg text-center">
                <CardContent className="p-8">
                  <div className="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6">
                    <Star className="h-8 w-8 text-purple-600" />
                  </div>
                  <h3 className="text-xl font-bold mb-4 text-gray-900">Gain Experience</h3>
                  <p className="text-gray-600">
                    Develop new skills in environmental science, community engagement, and 
                    project management while making a difference.
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Volunteer Opportunities */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold mb-6 text-gray-900">
                Volunteer Opportunities
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Choose from various volunteer roles that match your skills, interests, and availability
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Opportunity 1 */}
              <Card className="border-0 shadow-lg hover:shadow-xl transition-all">
                <CardHeader>
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="bg-green-100 p-3 rounded-lg">
                      <TreePine className="h-6 w-6 text-green-600" />
                    </div>
                    <div>
                      <Badge className="bg-green-100 text-green-800 mb-2">Field Work</Badge>
                      <CardTitle className="text-xl">Tree Planting Volunteer</CardTitle>
                    </div>
                  </div>
                  <CardDescription className="text-gray-600">
                    Join our reforestation efforts by participating in tree planting activities 
                    across various communities in Cameroon.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3 mb-6">
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <Clock className="h-4 w-4" />
                      <span>Weekends, 4-6 hours</span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <MapPin className="h-4 w-4" />
                      <span>Various locations around Limbe</span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <Users className="h-4 w-4" />
                      <span>Team-based activities</span>
                    </div>
                  </div>
                  <div className="space-y-2 mb-6">
                    <h4 className="font-semibold text-gray-900">Requirements:</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li className="flex items-center space-x-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span>Physical fitness for outdoor work</span>
                      </li>
                      <li className="flex items-center space-x-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span>Commitment to environmental conservation</span>
                      </li>
                      <li className="flex items-center space-x-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span>Ability to work in teams</span>
                      </li>
                    </ul>
                  </div>
                  <Button className="w-full bg-green-600 hover:bg-green-700 rounded-full">
                    Apply Now
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </CardContent>
              </Card>

              {/* Opportunity 2 */}
              <Card className="border-0 shadow-lg hover:shadow-xl transition-all">
                <CardHeader>
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="bg-blue-100 p-3 rounded-lg">
                      <Lightbulb className="h-6 w-6 text-blue-600" />
                    </div>
                    <div>
                      <Badge className="bg-blue-100 text-blue-800 mb-2">Education</Badge>
                      <CardTitle className="text-xl">Environmental Educator</CardTitle>
                    </div>
                  </div>
                  <CardDescription className="text-gray-600">
                    Help conduct educational workshops and awareness campaigns about climate change 
                    and environmental protection in local communities.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3 mb-6">
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <Clock className="h-4 w-4" />
                      <span>Flexible schedule, 3-5 hours per session</span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <MapPin className="h-4 w-4" />
                      <span>Schools and community centers</span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <Users className="h-4 w-4" />
                      <span>Work with diverse age groups</span>
                    </div>
                  </div>
                  <div className="space-y-2 mb-6">
                    <h4 className="font-semibold text-gray-900">Requirements:</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li className="flex items-center space-x-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span>Good communication skills</span>
                      </li>
                      <li className="flex items-center space-x-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span>Basic knowledge of environmental issues</span>
                      </li>
                      <li className="flex items-center space-x-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span>Patience and enthusiasm for teaching</span>
                      </li>
                    </ul>
                  </div>
                  <Button className="w-full bg-blue-600 hover:bg-blue-700 rounded-full">
                    Apply Now
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </CardContent>
              </Card>

              {/* Opportunity 3 */}
              <Card className="border-0 shadow-lg hover:shadow-xl transition-all">
                <CardHeader>
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="bg-purple-100 p-3 rounded-lg">
                      <Camera className="h-6 w-6 text-purple-600" />
                    </div>
                    <div>
                      <Badge className="bg-purple-100 text-purple-800 mb-2">Media</Badge>
                      <CardTitle className="text-xl">Content Creator</CardTitle>
                    </div>
                  </div>
                  <CardDescription className="text-gray-600">
                    Help document our environmental activities through photography, videography, 
                    and social media content creation.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3 mb-6">
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <Clock className="h-4 w-4" />
                      <span>Flexible, event-based</span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <MapPin className="h-4 w-4" />
                      <span>Various project locations</span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <Users className="h-4 w-4" />
                      <span>Independent and collaborative work</span>
                    </div>
                  </div>
                  <div className="space-y-2 mb-6">
                    <h4 className="font-semibold text-gray-900">Requirements:</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li className="flex items-center space-x-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span>Photography/videography skills</span>
                      </li>
                      <li className="flex items-center space-x-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span>Social media experience</span>
                      </li>
                      <li className="flex items-center space-x-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span>Creative storytelling ability</span>
                      </li>
                    </ul>
                  </div>
                  <Button className="w-full bg-purple-600 hover:bg-purple-700 rounded-full">
                    Apply Now
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </CardContent>
              </Card>

              {/* Opportunity 4 */}
              <Card className="border-0 shadow-lg hover:shadow-xl transition-all">
                <CardHeader>
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="bg-orange-100 p-3 rounded-lg">
                      <Megaphone className="h-6 w-6 text-orange-600" />
                    </div>
                    <div>
                      <Badge className="bg-orange-100 text-orange-800 mb-2">Outreach</Badge>
                      <CardTitle className="text-xl">Community Outreach Coordinator</CardTitle>
                    </div>
                  </div>
                  <CardDescription className="text-gray-600">
                    Help organize and coordinate community events, workshops, and awareness campaigns 
                    to expand our environmental impact.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3 mb-6">
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <Clock className="h-4 w-4" />
                      <span>10-15 hours per week</span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <MapPin className="h-4 w-4" />
                      <span>Office and field work</span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <Users className="h-4 w-4" />
                      <span>Leadership role</span>
                    </div>
                  </div>
                  <div className="space-y-2 mb-6">
                    <h4 className="font-semibold text-gray-900">Requirements:</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li className="flex items-center space-x-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span>Strong organizational skills</span>
                      </li>
                      <li className="flex items-center space-x-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span>Experience in event planning</span>
                      </li>
                      <li className="flex items-center space-x-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span>Leadership and communication skills</span>
                      </li>
                    </ul>
                  </div>
                  <Button className="w-full bg-orange-600 hover:bg-orange-700 rounded-full">
                    Apply Now
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Volunteer Testimonials */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold mb-6 text-gray-900">
                What Our Volunteers Say
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Hear from our amazing volunteers about their experiences with ACEF
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <Card className="border-0 shadow-lg">
                <CardContent className="p-8">
                  <Quote className="h-8 w-8 text-green-600 mb-4" />
                  <p className="text-gray-600 mb-6 italic">
                    "Volunteering with ACEF has been incredibly rewarding. I've learned so much about 
                    environmental conservation while making a real difference in my community."
                  </p>
                  <div className="flex items-center space-x-3">
                    <Avatar>
                      <AvatarFallback className="bg-green-100 text-green-800">MK</AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="font-medium text-gray-900">Marie Kone</div>
                      <div className="text-sm text-gray-600">Tree Planting Volunteer</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-lg">
                <CardContent className="p-8">
                  <Quote className="h-8 w-8 text-blue-600 mb-4" />
                  <p className="text-gray-600 mb-6 italic">
                    "The education programs have allowed me to share my passion for the environment 
                    with young people. It's amazing to see their enthusiasm grow."
                  </p>
                  <div className="flex items-center space-x-3">
                    <Avatar>
                      <AvatarFallback className="bg-blue-100 text-blue-800">PT</AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="font-medium text-gray-900">Paul Tabi</div>
                      <div className="text-sm text-gray-600">Environmental Educator</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-lg">
                <CardContent className="p-8">
                  <Quote className="h-8 w-8 text-purple-600 mb-4" />
                  <p className="text-gray-600 mb-6 italic">
                    "Being part of ACEF's content team has helped me develop my skills while 
                    documenting important environmental work. It's been a perfect fit."
                  </p>
                  <div className="flex items-center space-x-3">
                    <Avatar>
                      <AvatarFallback className="bg-purple-100 text-purple-800">AK</AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="font-medium text-gray-900">Amina Kone</div>
                      <div className="text-sm text-gray-600">Content Creator</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Volunteer Application Form */}
      <section className="py-16 bg-gradient-to-r from-green-600 to-blue-600 text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Ready to Make a Difference?
            </h2>
            <p className="text-xl mb-12 opacity-90">
              Join our team of dedicated volunteers and help us protect Africa's environment. 
              Fill out our application form to get started.
            </p>
            
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 mb-8">
              <h3 className="text-2xl font-bold mb-6">Quick Application</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <Input 
                  placeholder="Full Name" 
                  className="bg-white/20 border-white/30 text-white placeholder-white/70 rounded-full"
                />
                <Input 
                  placeholder="Email Address" 
                  className="bg-white/20 border-white/30 text-white placeholder-white/70 rounded-full"
                />
                <Input 
                  placeholder="Phone Number" 
                  className="bg-white/20 border-white/30 text-white placeholder-white/70 rounded-full"
                />
                <Input 
                  placeholder="Preferred Role" 
                  className="bg-white/20 border-white/30 text-white placeholder-white/70 rounded-full"
                />
              </div>
              <Button size="lg" className="bg-white text-green-600 hover:bg-gray-100 px-8 py-3 rounded-full">
                Submit Application
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </div>
            
            <p className="text-sm opacity-80">
              Or contact us <NAME_EMAIL> for more information
            </p>
          </div>
        </div>
      </section>
    </div>
  );
}
