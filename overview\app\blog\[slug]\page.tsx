import Image from "next/image";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import { 
  ArrowRight, 
  Calendar, 
  Clock, 
  User, 
  Tag, 
  Eye,
  MessageCircle,
  Share2,
  Heart,
  Bookmark,
  ArrowLeft
} from "lucide-react";

interface BlogPostPageProps {
  params: {
    slug: string;
  };
}

export default function BlogPostPage({ params }: BlogPostPageProps) {
  // In a real app, you would fetch the blog post data based on the slug
  const blogPost = {
    title: "The Future of Environmental Conservation in Africa: Lessons from Community-Led Initiatives",
    excerpt: "As Africa faces unprecedented environmental challenges, community-led conservation initiatives are emerging as powerful catalysts for change.",
    content: `
      <p>As Africa faces unprecedented environmental challenges, from deforestation to climate change impacts, a new wave of conservation efforts is emerging from an unexpected source: local communities themselves. These grassroots initiatives are not only proving effective but are also reshaping how we think about environmental protection across the continent.</p>

      <h2>The Power of Community Ownership</h2>
      <p>Traditional top-down conservation approaches have often failed to create lasting change because they overlooked the crucial role of local communities. However, when communities take ownership of conservation efforts, the results are remarkable. In Cameroon, we've witnessed firsthand how community-led tree planting initiatives have not only restored degraded landscapes but also strengthened social bonds and created economic opportunities.</p>

      <p>The Africa Climate and Environment Foundation (ACEF) has been privileged to support several such initiatives, and the lessons learned are invaluable. When communities see direct benefits from conservation efforts – whether through improved water quality, enhanced food security, or new income streams – they become the most effective guardians of their environment.</p>

      <h2>Success Stories from the Field</h2>
      <p>One of our most successful programs involved working with 15 communities across the South West Region of Cameroon. Rather than imposing external solutions, we facilitated community meetings where residents identified their own environmental priorities and developed action plans.</p>

      <p>The results exceeded all expectations. Communities not only planted over 500 trees but also established community nurseries, created local environmental committees, and developed sustainable harvesting practices for forest products. Most importantly, they took pride in their achievements and continued the work long after our formal program ended.</p>

      <h2>Challenges and Solutions</h2>
      <p>Community-led conservation isn't without challenges. Limited resources, competing priorities, and sometimes conflicting interests within communities can create obstacles. However, we've found that these challenges often lead to innovative solutions when communities are empowered to find their own answers.</p>

      <p>For instance, when one community struggled with funding for tree seedlings, they developed a rotating savings scheme where each household contributed monthly to a conservation fund. This not only solved the immediate funding issue but also created a sustainable financing mechanism for future projects.</p>

      <h2>The Role of Technology</h2>
      <p>Modern technology is playing an increasingly important role in supporting community conservation efforts. Mobile apps for monitoring forest cover, GPS devices for mapping protected areas, and social media platforms for sharing success stories are all helping communities document and scale their impact.</p>

      <p>At ACEF, we've been exploring how to integrate appropriate technology into our programs without overwhelming communities or creating dependency. The key is ensuring that technology serves the community's goals rather than driving them.</p>

      <h2>Looking Forward</h2>
      <p>The future of environmental conservation in Africa lies in recognizing and supporting the incredible potential of local communities. This doesn't mean abandoning scientific expertise or policy frameworks, but rather finding ways to integrate community knowledge and ownership into broader conservation strategies.</p>

      <p>As we continue our work at ACEF, we're committed to learning from communities, supporting their initiatives, and helping scale successful approaches. The path forward is clear: when communities lead, conservation succeeds.</p>
    `,
    author: {
      name: "Dr. Jane Doe",
      role: "Executive Director, ACEF",
      avatar: "JD"
    },
    publishedAt: "March 15, 2024",
    readTime: "8 min read",
    category: "Conservation",
    tags: ["Community Conservation", "Africa", "Sustainability", "Climate Action"],
    views: 1247,
    comments: 23,
    likes: 89,
    image: "https://images.unsplash.com/photo-1569163139394-de4e4f43e4e3?q=80&w=2070&auto=format&fit=crop"
  };

  return (
    <div className="flex flex-col">
      {/* Breadcrumb */}
      <section className="py-6 bg-gray-50 border-b">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink href="/">Home</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbLink href="/blog">Blog</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbPage>{blogPost.title.substring(0, 50)}...</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </div>
      </section>

      {/* Article Header */}
      <section className="py-12 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <Button variant="ghost" className="mb-6 p-0 h-auto text-gray-600 hover:text-green-600">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Blog
            </Button>

            <div className="mb-8">
              <Badge className="mb-4 bg-purple-100 text-purple-800">{blogPost.category}</Badge>
              <h1 className="text-3xl md:text-5xl font-bold mb-6 text-gray-900 leading-tight">
                {blogPost.title}
              </h1>
              <p className="text-xl text-gray-600 leading-relaxed mb-8">
                {blogPost.excerpt}
              </p>

              {/* Article Meta */}
              <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-6">
                <div className="flex items-center space-x-4">
                  <Avatar className="h-12 w-12">
                    <AvatarFallback className="bg-green-100 text-green-800 font-semibold">
                      {blogPost.author.avatar}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-semibold text-gray-900">{blogPost.author.name}</div>
                    <div className="text-sm text-gray-600">{blogPost.author.role}</div>
                  </div>
                </div>

                <div className="flex items-center space-x-6 text-sm text-gray-500">
                  <div className="flex items-center space-x-1">
                    <Calendar className="h-4 w-4" />
                    <span>{blogPost.publishedAt}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Clock className="h-4 w-4" />
                    <span>{blogPost.readTime}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Eye className="h-4 w-4" />
                    <span>{blogPost.views} views</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Featured Image */}
            <div className="aspect-video relative overflow-hidden rounded-2xl shadow-2xl mb-12">
              <Image
                src={blogPost.image}
                alt={blogPost.title}
                fill
                className="object-cover"
              />
            </div>

            {/* Article Actions */}
            <div className="flex items-center justify-between mb-12 p-6 bg-gray-50 rounded-xl">
              <div className="flex items-center space-x-4">
                <Button variant="outline" size="sm" className="rounded-full">
                  <Heart className="h-4 w-4 mr-2" />
                  {blogPost.likes}
                </Button>
                <Button variant="outline" size="sm" className="rounded-full">
                  <MessageCircle className="h-4 w-4 mr-2" />
                  {blogPost.comments}
                </Button>
                <Button variant="outline" size="sm" className="rounded-full">
                  <Bookmark className="h-4 w-4 mr-2" />
                  Save
                </Button>
              </div>
              <Button variant="outline" size="sm" className="rounded-full">
                <Share2 className="h-4 w-4 mr-2" />
                Share
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Article Content */}
      <section className="py-12 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div 
              className="prose prose-lg max-w-none prose-headings:text-gray-900 prose-p:text-gray-700 prose-p:leading-relaxed prose-a:text-green-600 prose-strong:text-gray-900"
              dangerouslySetInnerHTML={{ __html: blogPost.content }}
            />

            {/* Tags */}
            <div className="mt-12 pt-8 border-t">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Tags</h3>
              <div className="flex flex-wrap gap-2">
                {blogPost.tags.map((tag, index) => (
                  <Badge key={index} variant="secondary" className="text-sm">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>

            {/* Author Bio */}
            <Card className="mt-12 border-0 shadow-lg">
              <CardContent className="p-8">
                <div className="flex items-start space-x-4">
                  <Avatar className="h-16 w-16">
                    <AvatarFallback className="bg-green-100 text-green-800 font-semibold text-lg">
                      {blogPost.author.avatar}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <h3 className="text-xl font-bold text-gray-900 mb-2">{blogPost.author.name}</h3>
                    <p className="text-gray-600 mb-4">
                      {blogPost.author.role} with over 15 years of experience in environmental 
                      conservation and climate change research. Passionate about community-based 
                      conservation approaches and sustainable development in Africa.
                    </p>
                    <Button variant="outline" className="rounded-full">
                      View Profile
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Related Articles */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl font-bold text-gray-900 mb-12 text-center">Related Articles</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {/* Related Article 1 */}
              <Card className="border-0 shadow-lg hover:shadow-xl transition-all bg-white overflow-hidden group">
                <div className="aspect-video relative overflow-hidden">
                  <Image
                    src="https://images.unsplash.com/photo-1558618666-fcd25c85cd64?q=80&w=2070&auto=format&fit=crop"
                    alt="Related article"
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                </div>
                <CardContent className="p-6">
                  <h3 className="text-lg font-bold mb-2 text-gray-900 group-hover:text-green-600 transition-colors">
                    Climate Education: Empowering the Next Generation
                  </h3>
                  <p className="text-gray-600 text-sm mb-4">
                    How educational programs are creating environmental champions across Africa...
                  </p>
                  <Button variant="ghost" size="sm" className="p-0 h-auto text-green-600">
                    Read More
                    <ArrowRight className="ml-1 h-3 w-3" />
                  </Button>
                </CardContent>
              </Card>

              {/* Related Article 2 */}
              <Card className="border-0 shadow-lg hover:shadow-xl transition-all bg-white overflow-hidden group">
                <div className="aspect-video relative overflow-hidden">
                  <Image
                    src="https://images.unsplash.com/photo-1593113598332-cd288d649433?q=80&w=2070&auto=format&fit=crop"
                    alt="Related article"
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                </div>
                <CardContent className="p-6">
                  <h3 className="text-lg font-bold mb-2 text-gray-900 group-hover:text-green-600 transition-colors">
                    Building Partnerships for Conservation Success
                  </h3>
                  <p className="text-gray-600 text-sm mb-4">
                    The importance of collaboration in achieving environmental goals...
                  </p>
                  <Button variant="ghost" size="sm" className="p-0 h-auto text-green-600">
                    Read More
                    <ArrowRight className="ml-1 h-3 w-3" />
                  </Button>
                </CardContent>
              </Card>

              {/* Related Article 3 */}
              <Card className="border-0 shadow-lg hover:shadow-xl transition-all bg-white overflow-hidden group">
                <div className="aspect-video relative overflow-hidden">
                  <Image
                    src="https://images.unsplash.com/photo-1473773508845-188df298d2d1?q=80&w=2074&auto=format&fit=crop"
                    alt="Related article"
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                </div>
                <CardContent className="p-6">
                  <h3 className="text-lg font-bold mb-2 text-gray-900 group-hover:text-green-600 transition-colors">
                    Sustainable Agriculture: Feeding the Future
                  </h3>
                  <p className="text-gray-600 text-sm mb-4">
                    Innovative farming practices that protect the environment...
                  </p>
                  <Button variant="ghost" size="sm" className="p-0 h-auto text-green-600">
                    Read More
                    <ArrowRight className="ml-1 h-3 w-3" />
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
