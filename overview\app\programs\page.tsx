import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  ArrowRight, 
  Search, 
  MapPin, 
  Users, 
  Calendar, 
  Target, 
  TreePine, 
  Lightbulb, 
  Sun, 
  Droplets,
  Wind,
  Leaf,
  Award,
  CheckCircle,
  Filter,
  Grid,
  List
} from "lucide-react";

export default function ProgramsPage() {
  return (
    <div className="flex flex-col">
      {/* Page Header */}
      <section className="py-16 bg-gradient-to-r from-green-50 to-blue-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <Breadcrumb className="justify-center mb-6">
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink href="/">Home</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbPage>Programs & Projects</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
            <h1 className="text-4xl md:text-6xl font-bold mb-6 text-gray-900">
              Environmental Programs & Projects
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Discover our comprehensive environmental initiatives designed to create lasting impact 
              across Africa through conservation, education, and sustainable development
            </p>
          </div>
        </div>
      </section>

      {/* Search and Filter Section */}
      <section className="py-8 bg-white border-b">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
              <div className="flex items-center space-x-4 w-full lg:w-auto">
                <div className="relative flex-1 lg:w-80">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input 
                    placeholder="Search programs and projects..." 
                    className="pl-10 rounded-full border-gray-300"
                  />
                </div>
                <Button variant="outline" className="rounded-full">
                  <Filter className="h-4 w-4 mr-2" />
                  Filter
                </Button>
              </div>
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <Button variant="outline" size="icon" className="rounded-full">
                    <Grid className="h-4 w-4" />
                  </Button>
                  <Button variant="outline" size="icon" className="rounded-full">
                    <List className="h-4 w-4" />
                  </Button>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge variant="outline">All</Badge>
                  <Badge variant="outline">Active</Badge>
                  <Badge variant="outline">Completed</Badge>
                  <Badge variant="outline">Upcoming</Badge>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Programs Overview */}
      <section className="py-12 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
              <div className="text-center bg-white p-6 rounded-xl shadow-lg">
                <div className="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <TreePine className="h-8 w-8 text-green-600" />
                </div>
                <div className="text-3xl font-bold text-green-600 mb-1">8</div>
                <div className="text-sm text-gray-600">Active Programs</div>
              </div>
              <div className="text-center bg-white p-6 rounded-xl shadow-lg">
                <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Users className="h-8 w-8 text-blue-600" />
                </div>
                <div className="text-3xl font-bold text-blue-600 mb-1">1.2K+</div>
                <div className="text-sm text-gray-600">People Reached</div>
              </div>
              <div className="text-center bg-white p-6 rounded-xl shadow-lg">
                <div className="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <MapPin className="h-8 w-8 text-purple-600" />
                </div>
                <div className="text-3xl font-bold text-purple-600 mb-1">15+</div>
                <div className="text-sm text-gray-600">Communities</div>
              </div>
              <div className="text-center bg-white p-6 rounded-xl shadow-lg">
                <div className="bg-orange-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Award className="h-8 w-8 text-orange-600" />
                </div>
                <div className="text-3xl font-bold text-orange-600 mb-1">5</div>
                <div className="text-sm text-gray-600">Completed Projects</div>
              </div>
            </div>

            <Tabs defaultValue="all" className="w-full">
              <TabsList className="grid w-full grid-cols-5 mb-12 bg-white shadow-lg rounded-full p-2">
                <TabsTrigger value="all" className="rounded-full">All Programs</TabsTrigger>
                <TabsTrigger value="forest" className="rounded-full">Forest Conservation</TabsTrigger>
                <TabsTrigger value="education" className="rounded-full">Education</TabsTrigger>
                <TabsTrigger value="agriculture" className="rounded-full">Agriculture</TabsTrigger>
                <TabsTrigger value="climate" className="rounded-full">Climate Action</TabsTrigger>
              </TabsList>

              <TabsContent value="all" className="space-y-8">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                  {/* Program Card 1 */}
                  <Card className="group hover:shadow-2xl transition-all duration-300 border-0 shadow-lg overflow-hidden">
                    <div className="aspect-video bg-gradient-to-br from-green-400 to-green-600 relative overflow-hidden">
                      <div className="absolute inset-0 flex items-center justify-center">
                        <TreePine className="h-20 w-20 text-white opacity-80" />
                      </div>
                      <Badge className="absolute top-4 left-4 bg-white text-green-800">Active</Badge>
                      <div className="absolute top-4 right-4 bg-white/20 backdrop-blur-sm rounded-full px-3 py-1 text-white text-sm">
                        85% Complete
                      </div>
                    </div>
                    <CardHeader>
                      <div className="flex items-center justify-between mb-2">
                        <Badge variant="outline" className="text-green-600 border-green-600">Forest Conservation</Badge>
                        <div className="flex items-center space-x-1 text-sm text-gray-500">
                          <Calendar className="h-4 w-4" />
                          <span>2024</span>
                        </div>
                      </div>
                      <CardTitle className="text-xl group-hover:text-green-600 transition-colors">
                        Forest Restoration Initiative
                      </CardTitle>
                      <CardDescription className="text-gray-600">
                        Comprehensive reforestation and afforestation projects to combat deforestation 
                        and restore degraded forest areas across Cameroon.
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div className="flex items-center space-x-2">
                            <MapPin className="h-4 w-4 text-gray-400" />
                            <span className="text-gray-600">Limbe Region</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Users className="h-4 w-4 text-gray-400" />
                            <span className="text-gray-600">500+ volunteers</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Target className="h-4 w-4 text-gray-400" />
                            <span className="text-gray-600">500 trees planted</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <CheckCircle className="h-4 w-4 text-green-500" />
                            <span className="text-gray-600">On track</span>
                          </div>
                        </div>
                        
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-600">Progress</span>
                            <span className="font-medium">85%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div className="bg-green-600 h-2 rounded-full" style={{width: '85%'}}></div>
                          </div>
                        </div>
                        
                        <Link href="/programs/forest-restoration-initiative">
                          <Button className="w-full bg-green-600 hover:bg-green-700 rounded-full">
                            Learn More
                            <ArrowRight className="ml-2 h-4 w-4" />
                          </Button>
                        </Link>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Program Card 2 */}
                  <Card className="group hover:shadow-2xl transition-all duration-300 border-0 shadow-lg overflow-hidden">
                    <div className="aspect-video bg-gradient-to-br from-blue-400 to-blue-600 relative overflow-hidden">
                      <div className="absolute inset-0 flex items-center justify-center">
                        <Lightbulb className="h-20 w-20 text-white opacity-80" />
                      </div>
                      <Badge className="absolute top-4 left-4 bg-white text-blue-800">Active</Badge>
                      <div className="absolute top-4 right-4 bg-white/20 backdrop-blur-sm rounded-full px-3 py-1 text-white text-sm">
                        92% Complete
                      </div>
                    </div>
                    <CardHeader>
                      <div className="flex items-center justify-between mb-2">
                        <Badge variant="outline" className="text-blue-600 border-blue-600">Education</Badge>
                        <div className="flex items-center space-x-1 text-sm text-gray-500">
                          <Calendar className="h-4 w-4" />
                          <span>2024</span>
                        </div>
                      </div>
                      <CardTitle className="text-xl group-hover:text-blue-600 transition-colors">
                        Climate Education Program
                      </CardTitle>
                      <CardDescription className="text-gray-600">
                        Educational workshops and awareness campaigns on climate change and 
                        environmental protection for rural communities.
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div className="flex items-center space-x-2">
                            <MapPin className="h-4 w-4 text-gray-400" />
                            <span className="text-gray-600">15 Communities</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Users className="h-4 w-4 text-gray-400" />
                            <span className="text-gray-600">1,200+ reached</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Target className="h-4 w-4 text-gray-400" />
                            <span className="text-gray-600">25 workshops</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <CheckCircle className="h-4 w-4 text-green-500" />
                            <span className="text-gray-600">Excellent</span>
                          </div>
                        </div>
                        
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-600">Progress</span>
                            <span className="font-medium">92%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div className="bg-blue-600 h-2 rounded-full" style={{width: '92%'}}></div>
                          </div>
                        </div>
                        
                        <Button className="w-full bg-blue-600 hover:bg-blue-700 rounded-full">
                          Learn More
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Program Card 3 */}
                  <Card className="group hover:shadow-2xl transition-all duration-300 border-0 shadow-lg overflow-hidden">
                    <div className="aspect-video bg-gradient-to-br from-yellow-400 to-orange-500 relative overflow-hidden">
                      <div className="absolute inset-0 flex items-center justify-center">
                        <Sun className="h-20 w-20 text-white opacity-80" />
                      </div>
                      <Badge className="absolute top-4 left-4 bg-white text-orange-800">Active</Badge>
                      <div className="absolute top-4 right-4 bg-white/20 backdrop-blur-sm rounded-full px-3 py-1 text-white text-sm">
                        70% Complete
                      </div>
                    </div>
                    <CardHeader>
                      <div className="flex items-center justify-between mb-2">
                        <Badge variant="outline" className="text-orange-600 border-orange-600">Agriculture</Badge>
                        <div className="flex items-center space-x-1 text-sm text-gray-500">
                          <Calendar className="h-4 w-4" />
                          <span>2024</span>
                        </div>
                      </div>
                      <CardTitle className="text-xl group-hover:text-orange-600 transition-colors">
                        Sustainable Agriculture Initiative
                      </CardTitle>
                      <CardDescription className="text-gray-600">
                        Training programs for farmers on eco-friendly agricultural practices 
                        and sustainable farming techniques.
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div className="flex items-center space-x-2">
                            <MapPin className="h-4 w-4 text-gray-400" />
                            <span className="text-gray-600">Rural Areas</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Users className="h-4 w-4 text-gray-400" />
                            <span className="text-gray-600">200+ farmers</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Target className="h-4 w-4 text-gray-400" />
                            <span className="text-gray-600">15 training sessions</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <CheckCircle className="h-4 w-4 text-yellow-500" />
                            <span className="text-gray-600">In progress</span>
                          </div>
                        </div>
                        
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-600">Progress</span>
                            <span className="font-medium">70%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div className="bg-orange-500 h-2 rounded-full" style={{width: '70%'}}></div>
                          </div>
                        </div>
                        
                        <Button className="w-full bg-orange-600 hover:bg-orange-700 rounded-full">
                          Learn More
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Program Card 4 */}
                  <Card className="group hover:shadow-2xl transition-all duration-300 border-0 shadow-lg overflow-hidden">
                    <div className="aspect-video bg-gradient-to-br from-purple-400 to-purple-600 relative overflow-hidden">
                      <div className="absolute inset-0 flex items-center justify-center">
                        <Droplets className="h-20 w-20 text-white opacity-80" />
                      </div>
                      <Badge className="absolute top-4 left-4 bg-white text-purple-800">Upcoming</Badge>
                      <div className="absolute top-4 right-4 bg-white/20 backdrop-blur-sm rounded-full px-3 py-1 text-white text-sm">
                        Planning Phase
                      </div>
                    </div>
                    <CardHeader>
                      <div className="flex items-center justify-between mb-2">
                        <Badge variant="outline" className="text-purple-600 border-purple-600">Water Conservation</Badge>
                        <div className="flex items-center space-x-1 text-sm text-gray-500">
                          <Calendar className="h-4 w-4" />
                          <span>Q2 2024</span>
                        </div>
                      </div>
                      <CardTitle className="text-xl group-hover:text-purple-600 transition-colors">
                        Clean Water Access Project
                      </CardTitle>
                      <CardDescription className="text-gray-600">
                        Initiative to improve access to clean water and promote water conservation 
                        practices in rural communities.
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div className="flex items-center space-x-2">
                            <MapPin className="h-4 w-4 text-gray-400" />
                            <span className="text-gray-600">5 Villages</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Users className="h-4 w-4 text-gray-400" />
                            <span className="text-gray-600">800+ beneficiaries</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Target className="h-4 w-4 text-gray-400" />
                            <span className="text-gray-600">5 water points</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <CheckCircle className="h-4 w-4 text-gray-400" />
                            <span className="text-gray-600">Planning</span>
                          </div>
                        </div>
                        
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-600">Status</span>
                            <span className="font-medium">Planning Phase</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div className="bg-purple-600 h-2 rounded-full" style={{width: '15%'}}></div>
                          </div>
                        </div>
                        
                        <Button className="w-full bg-purple-600 hover:bg-purple-700 rounded-full">
                          Learn More
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Program Card 5 */}
                  <Card className="group hover:shadow-2xl transition-all duration-300 border-0 shadow-lg overflow-hidden">
                    <div className="aspect-video bg-gradient-to-br from-teal-400 to-teal-600 relative overflow-hidden">
                      <div className="absolute inset-0 flex items-center justify-center">
                        <Wind className="h-20 w-20 text-white opacity-80" />
                      </div>
                      <Badge className="absolute top-4 left-4 bg-white text-teal-800">Completed</Badge>
                      <div className="absolute top-4 right-4 bg-white/20 backdrop-blur-sm rounded-full px-3 py-1 text-white text-sm">
                        100% Complete
                      </div>
                    </div>
                    <CardHeader>
                      <div className="flex items-center justify-between mb-2">
                        <Badge variant="outline" className="text-teal-600 border-teal-600">Climate Action</Badge>
                        <div className="flex items-center space-x-1 text-sm text-gray-500">
                          <Calendar className="h-4 w-4" />
                          <span>2023</span>
                        </div>
                      </div>
                      <CardTitle className="text-xl group-hover:text-teal-600 transition-colors">
                        Carbon Footprint Reduction Campaign
                      </CardTitle>
                      <CardDescription className="text-gray-600">
                        Community-wide campaign to reduce carbon emissions through education 
                        and practical action steps.
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div className="flex items-center space-x-2">
                            <MapPin className="h-4 w-4 text-gray-400" />
                            <span className="text-gray-600">10 Communities</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Users className="h-4 w-4 text-gray-400" />
                            <span className="text-gray-600">600+ participants</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Target className="h-4 w-4 text-gray-400" />
                            <span className="text-gray-600">20% reduction</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <CheckCircle className="h-4 w-4 text-green-500" />
                            <span className="text-gray-600">Successful</span>
                          </div>
                        </div>
                        
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-600">Impact</span>
                            <span className="font-medium">Excellent</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div className="bg-teal-600 h-2 rounded-full" style={{width: '100%'}}></div>
                          </div>
                        </div>
                        
                        <Button className="w-full bg-teal-600 hover:bg-teal-700 rounded-full">
                          View Results
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Program Card 6 */}
                  <Card className="group hover:shadow-2xl transition-all duration-300 border-0 shadow-lg overflow-hidden">
                    <div className="aspect-video bg-gradient-to-br from-indigo-400 to-indigo-600 relative overflow-hidden">
                      <div className="absolute inset-0 flex items-center justify-center">
                        <Leaf className="h-20 w-20 text-white opacity-80" />
                      </div>
                      <Badge className="absolute top-4 left-4 bg-white text-indigo-800">Active</Badge>
                      <div className="absolute top-4 right-4 bg-white/20 backdrop-blur-sm rounded-full px-3 py-1 text-white text-sm">
                        60% Complete
                      </div>
                    </div>
                    <CardHeader>
                      <div className="flex items-center justify-between mb-2">
                        <Badge variant="outline" className="text-indigo-600 border-indigo-600">Biodiversity</Badge>
                        <div className="flex items-center space-x-1 text-sm text-gray-500">
                          <Calendar className="h-4 w-4" />
                          <span>2024</span>
                        </div>
                      </div>
                      <CardTitle className="text-xl group-hover:text-indigo-600 transition-colors">
                        Wildlife Protection Initiative
                      </CardTitle>
                      <CardDescription className="text-gray-600">
                        Comprehensive program to protect endangered wildlife species and 
                        preserve biodiversity in protected areas.
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div className="flex items-center space-x-2">
                            <MapPin className="h-4 w-4 text-gray-400" />
                            <span className="text-gray-600">3 Reserves</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Users className="h-4 w-4 text-gray-400" />
                            <span className="text-gray-600">50+ rangers</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Target className="h-4 w-4 text-gray-400" />
                            <span className="text-gray-600">5 species focus</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <CheckCircle className="h-4 w-4 text-yellow-500" />
                            <span className="text-gray-600">In progress</span>
                          </div>
                        </div>
                        
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-600">Progress</span>
                            <span className="font-medium">60%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div className="bg-indigo-600 h-2 rounded-full" style={{width: '60%'}}></div>
                          </div>
                        </div>
                        
                        <Button className="w-full bg-indigo-600 hover:bg-indigo-700 rounded-full">
                          Learn More
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
            </Tabs>

            {/* Load More */}
            <div className="text-center mt-12">
              <Button size="lg" variant="outline" className="border-2 border-green-600 text-green-600 hover:bg-green-600 hover:text-white px-8 py-3 rounded-full">
                Load More Programs
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
