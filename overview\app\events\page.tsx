
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  ArrowRight, 
  Search, 
  Calendar, 
  MapPin, 
  Clock, 
  Users, 
  Filter,
  Grid,
  List,
  Plus
} from "lucide-react";

export default function EventsPage() {
  return (
    <div className="flex flex-col">
      {/* Page Header */}
      <section className="py-16 bg-gradient-to-r from-purple-50 to-blue-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <Breadcrumb className="justify-center mb-6">
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink href="/">Home</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbPage>Events</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
            <h1 className="text-4xl md:text-6xl font-bold mb-6 text-gray-900">
              Environmental Events
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Join us at our upcoming environmental events, workshops, and community gatherings 
              dedicated to climate action and conservation
            </p>
          </div>
        </div>
      </section>

      {/* Search and Filter Section */}
      <section className="py-8 bg-white border-b">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
              <div className="flex items-center space-x-4 w-full lg:w-auto">
                <div className="relative flex-1 lg:w-80">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input 
                    placeholder="Search events..." 
                    className="pl-10 rounded-full border-gray-300"
                  />
                </div>
                <Button variant="outline" className="rounded-full">
                  <Filter className="h-4 w-4 mr-2" />
                  Filter
                </Button>
              </div>
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <Button variant="outline" size="icon" className="rounded-full">
                    <Grid className="h-4 w-4" />
                  </Button>
                  <Button variant="outline" size="icon" className="rounded-full">
                    <List className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Events Content */}
      <section className="py-12 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <Tabs defaultValue="upcoming" className="w-full">
              <TabsList className="grid w-full grid-cols-3 mb-12 bg-white shadow-lg rounded-full p-2">
                <TabsTrigger value="upcoming" className="rounded-full">Upcoming Events</TabsTrigger>
                <TabsTrigger value="past" className="rounded-full">Past Events</TabsTrigger>
                <TabsTrigger value="all" className="rounded-full">All Events</TabsTrigger>
              </TabsList>

              <TabsContent value="upcoming" className="space-y-8">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                  {/* Event Card 1 */}
                  <Card className="group hover:shadow-2xl transition-all duration-300 border-0 shadow-lg overflow-hidden">
                    <div className="aspect-video bg-gradient-to-br from-green-400 to-green-600 relative overflow-hidden">
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="text-center text-white">
                          <div className="text-3xl font-bold mb-2">25</div>
                          <div className="text-lg">APR</div>
                        </div>
                      </div>
                      <Badge className="absolute top-4 left-4 bg-white text-green-800">Workshop</Badge>
                      <Badge className="absolute top-4 right-4 bg-red-500 text-white">Free</Badge>
                    </div>
                    <CardHeader>
                      <CardTitle className="text-xl group-hover:text-green-600 transition-colors">
                        Community Tree Planting Workshop
                      </CardTitle>
                      <CardDescription className="text-gray-600">
                        Join us for a hands-on workshop on tree planting techniques and forest conservation
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="flex items-center space-x-2 text-sm text-gray-600">
                          <Calendar className="h-4 w-4" />
                          <span>April 25, 2024</span>
                        </div>
                        <div className="flex items-center space-x-2 text-sm text-gray-600">
                          <Clock className="h-4 w-4" />
                          <span>9:00 AM - 4:00 PM</span>
                        </div>
                        <div className="flex items-center space-x-2 text-sm text-gray-600">
                          <MapPin className="h-4 w-4" />
                          <span>Limbe Community Center</span>
                        </div>
                        <div className="flex items-center space-x-2 text-sm text-gray-600">
                          <Users className="h-4 w-4" />
                          <span>50 participants max</span>
                        </div>
                        <Button className="w-full bg-green-600 hover:bg-green-700 rounded-full mt-4">
                          Register Now
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Event Card 2 */}
                  <Card className="group hover:shadow-2xl transition-all duration-300 border-0 shadow-lg overflow-hidden">
                    <div className="aspect-video bg-gradient-to-br from-blue-400 to-blue-600 relative overflow-hidden">
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="text-center text-white">
                          <div className="text-3xl font-bold mb-2">15</div>
                          <div className="text-lg">MAY</div>
                        </div>
                      </div>
                      <Badge className="absolute top-4 left-4 bg-white text-blue-800">Conference</Badge>
                      <Badge className="absolute top-4 right-4 bg-yellow-500 text-white">$25</Badge>
                    </div>
                    <CardHeader>
                      <CardTitle className="text-xl group-hover:text-blue-600 transition-colors">
                        Climate Change Awareness Conference
                      </CardTitle>
                      <CardDescription className="text-gray-600">
                        Annual conference bringing together experts, activists, and community leaders
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="flex items-center space-x-2 text-sm text-gray-600">
                          <Calendar className="h-4 w-4" />
                          <span>May 15, 2024</span>
                        </div>
                        <div className="flex items-center space-x-2 text-sm text-gray-600">
                          <Clock className="h-4 w-4" />
                          <span>8:00 AM - 6:00 PM</span>
                        </div>
                        <div className="flex items-center space-x-2 text-sm text-gray-600">
                          <MapPin className="h-4 w-4" />
                          <span>Douala Convention Center</span>
                        </div>
                        <div className="flex items-center space-x-2 text-sm text-gray-600">
                          <Users className="h-4 w-4" />
                          <span>200 participants max</span>
                        </div>
                        <Button className="w-full bg-blue-600 hover:bg-blue-700 rounded-full mt-4">
                          Register Now
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Event Card 3 */}
                  <Card className="group hover:shadow-2xl transition-all duration-300 border-0 shadow-lg overflow-hidden">
                    <div className="aspect-video bg-gradient-to-br from-purple-400 to-purple-600 relative overflow-hidden">
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="text-center text-white">
                          <div className="text-3xl font-bold mb-2">05</div>
                          <div className="text-lg">JUN</div>
                        </div>
                      </div>
                      <Badge className="absolute top-4 left-4 bg-white text-purple-800">Fundraiser</Badge>
                      <Badge className="absolute top-4 right-4 bg-green-500 text-white">Free</Badge>
                    </div>
                    <CardHeader>
                      <CardTitle className="text-xl group-hover:text-purple-600 transition-colors">
                        World Environment Day Celebration
                      </CardTitle>
                      <CardDescription className="text-gray-600">
                        Community celebration with activities, exhibitions, and environmental pledges
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="flex items-center space-x-2 text-sm text-gray-600">
                          <Calendar className="h-4 w-4" />
                          <span>June 5, 2024</span>
                        </div>
                        <div className="flex items-center space-x-2 text-sm text-gray-600">
                          <Clock className="h-4 w-4" />
                          <span>10:00 AM - 8:00 PM</span>
                        </div>
                        <div className="flex items-center space-x-2 text-sm text-gray-600">
                          <MapPin className="h-4 w-4" />
                          <span>Limbe Botanical Garden</span>
                        </div>
                        <div className="flex items-center space-x-2 text-sm text-gray-600">
                          <Users className="h-4 w-4" />
                          <span>Open to all</span>
                        </div>
                        <Button className="w-full bg-purple-600 hover:bg-purple-700 rounded-full mt-4">
                          Learn More
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="past" className="space-y-8">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                  {/* Past Event Card 1 */}
                  <Card className="border-0 shadow-lg bg-white opacity-75">
                    <div className="aspect-video bg-gradient-to-br from-gray-400 to-gray-600 relative overflow-hidden">
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="text-center text-white">
                          <div className="text-3xl font-bold mb-2">15</div>
                          <div className="text-lg">MAR</div>
                        </div>
                      </div>
                      <Badge className="absolute top-4 left-4 bg-white text-gray-800">Completed</Badge>
                    </div>
                    <CardHeader>
                      <CardTitle className="text-xl text-gray-700">
                        Forest Restoration Training
                      </CardTitle>
                      <CardDescription className="text-gray-600">
                        Training session on forest restoration techniques for local communities
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="flex items-center space-x-2 text-sm text-gray-600">
                          <Calendar className="h-4 w-4" />
                          <span>March 15, 2024</span>
                        </div>
                        <div className="flex items-center space-x-2 text-sm text-gray-600">
                          <Users className="h-4 w-4" />
                          <span>45 participants attended</span>
                        </div>
                        <Button variant="outline" className="w-full rounded-full mt-4">
                          View Summary
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
            </Tabs>

            {/* Event Calendar Section */}
            <div className="mt-16 bg-white rounded-2xl shadow-xl p-8">
              <div className="text-center mb-8">
                <h2 className="text-3xl font-bold text-gray-900 mb-4">Event Calendar</h2>
                <p className="text-gray-600">View all our events in calendar format</p>
              </div>
              
              {/* Simple Calendar Placeholder */}
              <div className="bg-gray-50 rounded-xl p-8 text-center">
                <Calendar className="h-24 w-24 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-700 mb-2">Interactive Calendar</h3>
                <p className="text-gray-600 mb-6">
                  View all upcoming events, workshops, and conferences in an interactive calendar format
                </p>
                <Button className="bg-purple-600 hover:bg-purple-700 rounded-full">
                  View Full Calendar
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* Newsletter Signup */}
            <div className="mt-16 bg-gradient-to-r from-green-600 to-blue-600 rounded-2xl shadow-xl p-8 text-white text-center">
              <h2 className="text-3xl font-bold mb-4">Stay Updated</h2>
              <p className="text-xl mb-8 opacity-90">
                Subscribe to our newsletter to receive updates about upcoming environmental events
              </p>
              <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                <Input 
                  placeholder="Enter your email" 
                  className="flex-1 bg-white text-gray-900 border-0 rounded-full"
                />
                <Button className="bg-white text-green-600 hover:bg-gray-100 rounded-full px-8">
                  Subscribe
                  <Plus className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
