import Image from "next/image";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { 
  ArrowRight, 
  Calendar, 
  MapPin, 
  Users, 
  Target, 
  CheckCircle,
  Clock,
  DollarSign,
  ArrowLeft,
  TreePine,
  Award,
  Camera,
  FileText
} from "lucide-react";

interface ProgramPageProps {
  params: {
    slug: string;
  };
}

export default function ProgramPage({ params }: ProgramPageProps) {
  // In a real app, you would fetch the program data based on the slug
  const program = {
    title: "Forest Restoration Initiative",
    description: "Comprehensive reforestation and afforestation projects to combat deforestation and restore degraded forest areas across Cameroon.",
    status: "Active",
    category: "Forest Conservation",
    startDate: "January 2024",
    endDate: "December 2024",
    location: "Limbe Region, South West Cameroon",
    budget: "$50,000",
    progress: 85,
    beneficiaries: "500+ community members",
    volunteers: "200+ active volunteers",
    treesPlanted: 500,
    targetTrees: 1000,
    communitiesReached: 15,
    objectives: [
      "Plant 1,000 native trees across 15 communities",
      "Train 200 community volunteers in tree planting techniques",
      "Establish 5 community tree nurseries",
      "Create sustainable forest management plans",
      "Raise awareness about deforestation impacts"
    ],
    achievements: [
      "500+ trees successfully planted",
      "200+ volunteers trained and certified",
      "3 community nurseries established",
      "15 communities actively participating",
      "85% tree survival rate achieved"
    ],
    challenges: [
      "Seasonal weather variations affecting planting schedules",
      "Limited access to remote communities during rainy season",
      "Need for ongoing maintenance and care of young trees"
    ],
    partners: [
      "Local Community Leaders",
      "Cameroon Ministry of Environment",
      "University of Buea - Environmental Sciences",
      "Local Women's Cooperatives"
    ],
    teamMembers: [
      { name: "Dr. Jane Doe", role: "Program Director", avatar: "JD" },
      { name: "Michael Smith", role: "Field Coordinator", avatar: "MS" },
      { name: "Paul Tabi", role: "Community Liaison", avatar: "PT" },
      { name: "Amina Kone", role: "Training Specialist", avatar: "AK" }
    ],
    images: [
      "https://images.unsplash.com/photo-1574263867128-a3d5c1b1dedc?q=80&w=2070&auto=format&fit=crop",
      "https://images.unsplash.com/photo-1542601906990-b4d3fb778b09?q=80&w=2013&auto=format&fit=crop",
      "https://images.unsplash.com/photo-1416879595882-3373a0480b5b?q=80&w=2070&auto=format&fit=crop"
    ]
  };

  return (
    <div className="flex flex-col">
      {/* Breadcrumb */}
      <section className="py-6 bg-gray-50 border-b">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink href="/">Home</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbLink href="/programs">Programs</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbPage>{program.title}</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </div>
      </section>

      {/* Program Header */}
      <section className="py-12 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <Button variant="ghost" className="mb-6 p-0 h-auto text-gray-600 hover:text-green-600">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Programs
            </Button>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
              {/* Main Content */}
              <div className="lg:col-span-2">
                <div className="mb-8">
                  <div className="flex items-center space-x-3 mb-4">
                    <Badge className="bg-green-100 text-green-800">{program.category}</Badge>
                    <Badge className="bg-blue-100 text-blue-800">{program.status}</Badge>
                  </div>
                  
                  <h1 className="text-3xl md:text-4xl font-bold mb-6 text-gray-900 leading-tight">
                    {program.title}
                  </h1>
                  
                  <p className="text-xl text-gray-600 leading-relaxed mb-8">
                    {program.description}
                  </p>

                  {/* Key Metrics */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12">
                    <div className="text-center bg-green-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-green-600 mb-1">{program.treesPlanted}</div>
                      <div className="text-sm text-gray-600">Trees Planted</div>
                    </div>
                    <div className="text-center bg-blue-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600 mb-1">{program.communitiesReached}</div>
                      <div className="text-sm text-gray-600">Communities</div>
                    </div>
                    <div className="text-center bg-purple-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-purple-600 mb-1">200+</div>
                      <div className="text-sm text-gray-600">Volunteers</div>
                    </div>
                    <div className="text-center bg-orange-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-orange-600 mb-1">{program.progress}%</div>
                      <div className="text-sm text-gray-600">Complete</div>
                    </div>
                  </div>

                  {/* Progress Section */}
                  <Card className="border-0 shadow-lg mb-8">
                    <CardHeader>
                      <CardTitle className="flex items-center">
                        <Target className="mr-2 h-5 w-5 text-green-600" />
                        Program Progress
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600">Overall Progress</span>
                          <span className="font-medium">{program.progress}%</span>
                        </div>
                        <Progress value={program.progress} className="h-3" />
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
                          <div>
                            <div className="text-sm text-gray-600 mb-1">Trees Planted</div>
                            <div className="text-lg font-semibold">{program.treesPlanted} / {program.targetTrees}</div>
                            <Progress value={(program.treesPlanted / program.targetTrees) * 100} className="h-2 mt-1" />
                          </div>
                          <div>
                            <div className="text-sm text-gray-600 mb-1">Communities Reached</div>
                            <div className="text-lg font-semibold">{program.communitiesReached} / 15</div>
                            <Progress value={100} className="h-2 mt-1" />
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Tabs for different sections */}
                <Tabs defaultValue="overview" className="w-full">
                  <TabsList className="grid w-full grid-cols-4">
                    <TabsTrigger value="overview">Overview</TabsTrigger>
                    <TabsTrigger value="achievements">Achievements</TabsTrigger>
                    <TabsTrigger value="gallery">Gallery</TabsTrigger>
                    <TabsTrigger value="team">Team</TabsTrigger>
                  </TabsList>

                  <TabsContent value="overview" className="mt-8">
                    <div className="space-y-8">
                      <Card className="border-0 shadow-lg">
                        <CardHeader>
                          <CardTitle>Program Objectives</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <ul className="space-y-3">
                            {program.objectives.map((objective, index) => (
                              <li key={index} className="flex items-start space-x-3">
                                <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                                <span className="text-gray-700">{objective}</span>
                              </li>
                            ))}
                          </ul>
                        </CardContent>
                      </Card>

                      <Card className="border-0 shadow-lg">
                        <CardHeader>
                          <CardTitle>Current Challenges</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <ul className="space-y-3">
                            {program.challenges.map((challenge, index) => (
                              <li key={index} className="flex items-start space-x-3">
                                <div className="w-2 h-2 bg-orange-500 rounded-full mt-2 flex-shrink-0"></div>
                                <span className="text-gray-700">{challenge}</span>
                              </li>
                            ))}
                          </ul>
                        </CardContent>
                      </Card>

                      <Card className="border-0 shadow-lg">
                        <CardHeader>
                          <CardTitle>Partners & Collaborators</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {program.partners.map((partner, index) => (
                              <div key={index} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                                <Award className="h-5 w-5 text-blue-500" />
                                <span className="text-gray-700">{partner}</span>
                              </div>
                            ))}
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  </TabsContent>

                  <TabsContent value="achievements" className="mt-8">
                    <Card className="border-0 shadow-lg">
                      <CardHeader>
                        <CardTitle>Key Achievements</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-6">
                          {program.achievements.map((achievement, index) => (
                            <div key={index} className="flex items-start space-x-4 p-4 bg-green-50 rounded-lg">
                              <div className="bg-green-500 text-white rounded-full p-2">
                                <CheckCircle className="h-4 w-4" />
                              </div>
                              <div>
                                <h3 className="font-semibold text-gray-900 mb-1">Achievement #{index + 1}</h3>
                                <p className="text-gray-700">{achievement}</p>
                              </div>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>

                  <TabsContent value="gallery" className="mt-8">
                    <Card className="border-0 shadow-lg">
                      <CardHeader>
                        <CardTitle className="flex items-center">
                          <Camera className="mr-2 h-5 w-5" />
                          Program Gallery
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                          {program.images.map((image, index) => (
                            <div key={index} className="aspect-square relative overflow-hidden rounded-lg group">
                              <Image
                                src={image}
                                alt={`Program activity ${index + 1}`}
                                fill
                                className="object-cover group-hover:scale-105 transition-transform duration-300"
                              />
                              <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300"></div>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>

                  <TabsContent value="team" className="mt-8">
                    <Card className="border-0 shadow-lg">
                      <CardHeader>
                        <CardTitle>Program Team</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          {program.teamMembers.map((member, index) => (
                            <div key={index} className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
                              <Avatar className="h-12 w-12">
                                <AvatarFallback className="bg-green-100 text-green-800 font-semibold">
                                  {member.avatar}
                                </AvatarFallback>
                              </Avatar>
                              <div>
                                <h3 className="font-semibold text-gray-900">{member.name}</h3>
                                <p className="text-sm text-gray-600">{member.role}</p>
                              </div>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>
                </Tabs>
              </div>

              {/* Sidebar */}
              <div className="lg:col-span-1">
                <div className="space-y-6">
                  {/* Program Details */}
                  <Card className="border-0 shadow-lg">
                    <CardHeader>
                      <CardTitle className="text-lg">Program Details</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4 text-sm">
                        <div className="flex items-center space-x-2">
                          <Calendar className="h-4 w-4 text-gray-400" />
                          <div>
                            <div className="font-medium text-gray-900">Duration</div>
                            <div className="text-gray-600">{program.startDate} - {program.endDate}</div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <MapPin className="h-4 w-4 text-gray-400" />
                          <div>
                            <div className="font-medium text-gray-900">Location</div>
                            <div className="text-gray-600">{program.location}</div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <DollarSign className="h-4 w-4 text-gray-400" />
                          <div>
                            <div className="font-medium text-gray-900">Budget</div>
                            <div className="text-gray-600">{program.budget}</div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Users className="h-4 w-4 text-gray-400" />
                          <div>
                            <div className="font-medium text-gray-900">Beneficiaries</div>
                            <div className="text-gray-600">{program.beneficiaries}</div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Get Involved */}
                  <Card className="border-0 shadow-lg bg-green-50">
                    <CardHeader>
                      <CardTitle className="text-lg text-green-800">Get Involved</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-green-700 mb-4">
                        Join our forest restoration efforts and make a direct impact on environmental conservation.
                      </p>
                      <div className="space-y-3">
                        <Button className="w-full bg-green-600 hover:bg-green-700">
                          Volunteer Now
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Button>
                        <Button variant="outline" className="w-full border-green-600 text-green-600 hover:bg-green-600 hover:text-white">
                          Donate to Program
                        </Button>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Program Updates */}
                  <Card className="border-0 shadow-lg">
                    <CardHeader>
                      <CardTitle className="text-lg">Recent Updates</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="border-l-4 border-green-500 pl-4">
                          <div className="text-sm text-gray-500 mb-1">March 15, 2024</div>
                          <h4 className="font-medium text-gray-900 text-sm">Milestone Reached</h4>
                          <p className="text-xs text-gray-600">Successfully planted 500th tree with community celebration</p>
                        </div>
                        <div className="border-l-4 border-blue-500 pl-4">
                          <div className="text-sm text-gray-500 mb-1">March 10, 2024</div>
                          <h4 className="font-medium text-gray-900 text-sm">New Partnership</h4>
                          <p className="text-xs text-gray-600">Collaboration with University of Buea established</p>
                        </div>
                        <div className="border-l-4 border-purple-500 pl-4">
                          <div className="text-sm text-gray-500 mb-1">March 5, 2024</div>
                          <h4 className="font-medium text-gray-900 text-sm">Training Completed</h4>
                          <p className="text-xs text-gray-600">200 volunteers completed tree care training program</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Related Programs */}
                  <Card className="border-0 shadow-lg">
                    <CardHeader>
                      <CardTitle className="text-lg">Related Programs</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium text-gray-900 text-sm mb-1">Climate Education Program</h4>
                          <p className="text-xs text-gray-600 mb-2">Educational workshops on environmental awareness</p>
                          <Button variant="ghost" size="sm" className="p-0 h-auto text-green-600 text-xs">
                            View Program
                            <ArrowRight className="ml-1 h-3 w-3" />
                          </Button>
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900 text-sm mb-1">Sustainable Agriculture Initiative</h4>
                          <p className="text-xs text-gray-600 mb-2">Training farmers in eco-friendly practices</p>
                          <Button variant="ghost" size="sm" className="p-0 h-auto text-green-600 text-xs">
                            View Program
                            <ArrowRight className="ml-1 h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
