import Image from "next/image";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  ArrowRight,
  Leaf,
  Users,
  Globe,
  Heart,
  TreePine,
  Droplets,
  Wind,
  Sun,
  Award,
  Calendar,
  MapPin,
  Quote,
  Play,
  CheckCircle,
  Target,
  Eye,
  Lightbulb,
  Megaphone,
  X
} from "lucide-react";

export default function Home() {
  return (
    <div className="flex flex-col">
      {/* News Banner */}
      <Alert className="rounded-none border-0 bg-gradient-to-r from-green-600 to-blue-600 text-white">
        <Megaphone className="h-4 w-4" />
        <AlertDescription className="flex items-center justify-between w-full">
          <Link href="/news/acef-surpasses-500-trees-planted-milestone" className="flex items-center space-x-2 hover:underline">
            <strong>Latest News:</strong>
            <span>ACEF reaches 500+ trees planted milestone! Join our World Environment Day celebration on June 5th.</span>
          </Link>
          <Button variant="ghost" size="sm" className="text-white hover:bg-white/20 p-1">
            <X className="h-4 w-4" />
          </Button>
        </AlertDescription>
      </Alert>

      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0 z-0">
          <div className="absolute inset-0 bg-gradient-to-r from-green-900/80 via-green-800/70 to-blue-900/80 z-10"></div>
          <div className="w-full h-full bg-gradient-to-b from-transparent via-transparent to-black/20 z-20 absolute"></div>
          {/* Real environmental image */}
          <Image
            src="https://images.unsplash.com/photo-1441974231531-c6227db76b6e?q=80&w=2071&auto=format&fit=crop"
            alt="African forest landscape"
            fill
            className="object-cover"
            priority
          />
        </div>

        {/* Hero Content */}
        <div className="relative z-30 container mx-auto px-4 text-center text-white">
          <div className="max-w-5xl mx-auto">
            <div className="mb-6">
              <Badge className="bg-white/20 text-white border-white/30 mb-4">
                Founded March 31, 2021
              </Badge>
            </div>
            <h1 className="text-5xl md:text-7xl font-bold mb-8 leading-tight">
              Protecting Africa's
              <span className="block text-green-300">Environment</span>
              <span className="block">for Future Generations</span>
            </h1>
            <p className="text-xl md:text-2xl mb-12 opacity-90 max-w-3xl mx-auto leading-relaxed">
              Africa Climate and Environment Foundation is dedicated to environmental conservation,
              climate action, and sustainable development across Cameroon and Africa.
            </p>
            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
              <Button size="lg" className="bg-green-600 hover:bg-green-700 text-white px-8 py-4 text-lg rounded-full shadow-2xl hover:shadow-3xl transition-all">
                <Heart className="mr-3 h-6 w-6" />
                Donate Now
              </Button>
              <Button size="lg" variant="outline" className="border-2 border-white text-white hover:bg-white hover:text-green-800 px-8 py-4 text-lg rounded-full">
                <Users className="mr-3 h-6 w-6" />
                Join Our Mission
                <ArrowRight className="ml-3 h-6 w-6" />
              </Button>
            </div>

            {/* Hero Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-16 pt-16 border-t border-white/20">
              <div className="text-center">
                <div className="text-4xl md:text-5xl font-bold text-green-300 mb-2">500+</div>
                <div className="text-lg opacity-90">Trees Planted</div>
              </div>
              <div className="text-center">
                <div className="text-4xl md:text-5xl font-bold text-blue-300 mb-2">1,200+</div>
                <div className="text-lg opacity-90">People Reached</div>
              </div>
              <div className="text-center">
                <div className="text-4xl md:text-5xl font-bold text-yellow-300 mb-2">15+</div>
                <div className="text-lg opacity-90">Communities Served</div>
              </div>
            </div>
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-30">
          <div className="animate-bounce">
            <ArrowRight className="h-6 w-6 text-white rotate-90" />
          </div>
        </div>
      </section>

      {/* About Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
              <div>
                <Badge className="mb-4 bg-green-100 text-green-800">About ACEF</Badge>
                <h2 className="text-4xl md:text-5xl font-bold mb-6 text-gray-900">
                  Leading Environmental Change in Africa
                </h2>
                <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                  Founded on March 31st, 2021, with headquarters in Limbe, Cameroon, ACEF is committed to
                  addressing climate change challenges and promoting environmental sustainability across Africa.
                  We work with communities, governments, and organizations to create lasting environmental impact.
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                  <div className="flex items-start space-x-3">
                    <div className="bg-green-100 p-2 rounded-lg">
                      <Target className="h-6 w-6 text-green-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-1">Our Mission</h3>
                      <p className="text-sm text-gray-600">Environmental conservation and climate action</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <div className="bg-blue-100 p-2 rounded-lg">
                      <Eye className="h-6 w-6 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-1">Our Vision</h3>
                      <p className="text-sm text-gray-600">Sustainable and resilient African communities</p>
                    </div>
                  </div>
                </div>

                <Button className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-full">
                  Learn More About Us
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </div>

              <div className="relative">
                {/* Real environmental conservation image */}
                <div className="aspect-square rounded-2xl shadow-2xl overflow-hidden relative">
                  <Image
                    src="https://images.unsplash.com/photo-1542601906990-b4d3fb778b09?q=80&w=2013&auto=format&fit=crop"
                    alt="Community tree planting activity"
                    fill
                    className="object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>
                </div>
                {/* Floating elements */}
                <div className="absolute -top-6 -right-6 bg-white p-4 rounded-xl shadow-lg">
                  <Award className="h-8 w-8 text-yellow-500" />
                </div>
                <div className="absolute -bottom-6 -left-6 bg-white p-4 rounded-xl shadow-lg">
                  <Globe className="h-8 w-8 text-blue-500" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Programs Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <Badge className="mb-4 bg-green-100 text-green-800">Our Programs</Badge>
              <h2 className="text-4xl md:text-5xl font-bold mb-6 text-gray-900">
                Environmental Initiatives Making a Difference
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Discover our comprehensive environmental programs designed to create lasting impact across Africa
              </p>
            </div>

            <Tabs defaultValue="all" className="w-full">
              <TabsList className="grid w-full grid-cols-4 mb-12 bg-white shadow-lg rounded-full p-2">
                <TabsTrigger value="all" className="rounded-full">All Programs</TabsTrigger>
                <TabsTrigger value="forest" className="rounded-full">Forest</TabsTrigger>
                <TabsTrigger value="climate" className="rounded-full">Climate</TabsTrigger>
                <TabsTrigger value="community" className="rounded-full">Community</TabsTrigger>
              </TabsList>

              <TabsContent value="all" className="space-y-8">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                  <Card className="group hover:shadow-2xl transition-all duration-300 border-0 shadow-lg">
                    <div className="aspect-video rounded-t-lg relative overflow-hidden">
                      <Image
                        src="https://images.unsplash.com/photo-1574263867128-a3d5c1b1dedc?q=80&w=2070&auto=format&fit=crop"
                        alt="Forest restoration project"
                        fill
                        className="object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                      <Badge className="absolute top-4 left-4 bg-white text-green-800">Active</Badge>
                    </div>
                    <CardHeader>
                      <CardTitle className="text-xl">Forest Restoration Initiative</CardTitle>
                      <CardDescription className="text-gray-600">
                        Comprehensive reforestation and afforestation projects across Cameroon
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="flex items-center space-x-2 text-sm text-gray-600">
                          <MapPin className="h-4 w-4" />
                          <span>Limbe, South West Region</span>
                        </div>
                        <div className="flex items-center space-x-2 text-sm text-gray-600">
                          <Users className="h-4 w-4" />
                          <span>500+ volunteers involved</span>
                        </div>
                        <Link href="/programs/forest-restoration-initiative">
                          <Button className="w-full bg-green-600 hover:bg-green-700 rounded-full">
                            Learn More
                            <ArrowRight className="ml-2 h-4 w-4" />
                          </Button>
                        </Link>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="group hover:shadow-2xl transition-all duration-300 border-0 shadow-lg">
                    <div className="aspect-video rounded-t-lg relative overflow-hidden">
                      <Image
                        src="https://images.unsplash.com/photo-1509909756405-be0199881695?q=80&w=2070&auto=format&fit=crop"
                        alt="Climate education workshop"
                        fill
                        className="object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                      <Badge className="absolute top-4 left-4 bg-white text-blue-800">Active</Badge>
                    </div>
                    <CardHeader>
                      <CardTitle className="text-xl">Climate Education Program</CardTitle>
                      <CardDescription className="text-gray-600">
                        Educational workshops on climate change awareness and environmental protection
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="flex items-center space-x-2 text-sm text-gray-600">
                          <MapPin className="h-4 w-4" />
                          <span>15+ Communities</span>
                        </div>
                        <div className="flex items-center space-x-2 text-sm text-gray-600">
                          <Users className="h-4 w-4" />
                          <span>1,200+ people reached</span>
                        </div>
                        <Link href="/programs/climate-education-program">
                          <Button className="w-full bg-blue-600 hover:bg-blue-700 rounded-full">
                            Learn More
                            <ArrowRight className="ml-2 h-4 w-4" />
                          </Button>
                        </Link>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="group hover:shadow-2xl transition-all duration-300 border-0 shadow-lg">
                    <div className="aspect-video rounded-t-lg relative overflow-hidden">
                      <Image
                        src="https://images.unsplash.com/photo-1500382017468-9049fed747ef?q=80&w=2089&auto=format&fit=crop"
                        alt="Sustainable agriculture farming"
                        fill
                        className="object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                      <Badge className="absolute top-4 left-4 bg-white text-orange-800">Active</Badge>
                    </div>
                    <CardHeader>
                      <CardTitle className="text-xl">Sustainable Agriculture</CardTitle>
                      <CardDescription className="text-gray-600">
                        Promoting eco-friendly farming practices and food security initiatives
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="flex items-center space-x-2 text-sm text-gray-600">
                          <MapPin className="h-4 w-4" />
                          <span>Rural Communities</span>
                        </div>
                        <div className="flex items-center space-x-2 text-sm text-gray-600">
                          <Users className="h-4 w-4" />
                          <span>200+ farmers trained</span>
                        </div>
                        <Link href="/programs/sustainable-agriculture-initiative">
                          <Button className="w-full bg-orange-600 hover:bg-orange-700 rounded-full">
                            Learn More
                            <ArrowRight className="ml-2 h-4 w-4" />
                          </Button>
                        </Link>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
            </Tabs>

            <div className="text-center mt-12">
              <Link href="/programs">
                <Button size="lg" variant="outline" className="border-2 border-green-600 text-green-600 hover:bg-green-600 hover:text-white px-8 py-3 rounded-full">
                  View All Programs
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Impact Section */}
      <section className="py-20 bg-green-600 text-white relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-green-600 to-green-800"></div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Our Environmental Impact
              </h2>
              <p className="text-xl opacity-90 max-w-3xl mx-auto">
                Measurable results from our dedicated environmental conservation efforts across Africa
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              <div className="text-center bg-white/10 backdrop-blur-sm rounded-2xl p-8 hover:bg-white/20 transition-all">
                <div className="bg-white/20 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <TreePine className="h-8 w-8" />
                </div>
                <div className="text-4xl font-bold mb-2">500+</div>
                <div className="text-lg opacity-90">Trees Planted</div>
                <div className="text-sm opacity-70 mt-2">Across 15 communities</div>
              </div>

              <div className="text-center bg-white/10 backdrop-blur-sm rounded-2xl p-8 hover:bg-white/20 transition-all">
                <div className="bg-white/20 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Users className="h-8 w-8" />
                </div>
                <div className="text-4xl font-bold mb-2">1,200+</div>
                <div className="text-lg opacity-90">People Educated</div>
                <div className="text-sm opacity-70 mt-2">On climate awareness</div>
              </div>

              <div className="text-center bg-white/10 backdrop-blur-sm rounded-2xl p-8 hover:bg-white/20 transition-all">
                <div className="bg-white/20 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Globe className="h-8 w-8" />
                </div>
                <div className="text-4xl font-bold mb-2">15+</div>
                <div className="text-lg opacity-90">Communities</div>
                <div className="text-sm opacity-70 mt-2">Actively engaged</div>
              </div>

              <div className="text-center bg-white/10 backdrop-blur-sm rounded-2xl p-8 hover:bg-white/20 transition-all">
                <div className="bg-white/20 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Award className="h-8 w-8" />
                </div>
                <div className="text-4xl font-bold mb-2">3+</div>
                <div className="text-lg opacity-90">Years</div>
                <div className="text-sm opacity-70 mt-2">Of dedicated service</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Latest News Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <Badge className="mb-4 bg-blue-100 text-blue-800">Latest Updates</Badge>
              <h2 className="text-4xl md:text-5xl font-bold mb-6 text-gray-900">
                News & Environmental Stories
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Stay informed about our latest environmental initiatives and impact stories from across Africa
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Featured Article */}
              <div className="lg:col-span-2">
                <Card className="border-0 shadow-xl overflow-hidden">
                  <div className="aspect-video relative overflow-hidden">
                    <Image
                      src="https://images.unsplash.com/photo-1416879595882-3373a0480b5b?q=80&w=2070&auto=format&fit=crop"
                      alt="Tree planting community event"
                      fill
                      className="object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                    <Badge className="absolute top-4 left-4 bg-red-500 text-white">Featured</Badge>
                    <div className="absolute bottom-4 left-4 text-white">
                      <div className="text-sm opacity-80">March 15, 2024</div>
                    </div>
                  </div>
                  <CardContent className="p-8">
                    <h3 className="text-2xl font-bold mb-4 text-gray-900">
                      Major Tree Planting Initiative Reaches 500 Trees Milestone
                    </h3>
                    <p className="text-gray-600 mb-6 leading-relaxed">
                      Community volunteers joined ACEF in a massive tree planting initiative across Limbe,
                      marking a significant milestone in our forest restoration program. The initiative
                      involved over 200 local volunteers and planted native species to restore degraded areas.
                    </p>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <Avatar>
                          <AvatarFallback className="bg-green-100 text-green-800">JD</AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium text-gray-900">Dr. Jane Doe</div>
                          <div className="text-sm text-gray-600">Executive Director</div>
                        </div>
                      </div>
                      <Button variant="outline" className="rounded-full">
                        Read More
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Side Articles */}
              <div className="space-y-6">
                <Card className="border-0 shadow-lg">
                  <CardContent className="p-6">
                    <div className="text-sm text-gray-500 mb-2">March 10, 2024</div>
                    <h4 className="font-semibold text-gray-900 mb-3">
                      Climate Education Workshop Reaches Rural Communities
                    </h4>
                    <p className="text-sm text-gray-600 mb-4">
                      Successful workshop on climate change awareness conducted in rural communities...
                    </p>
                    <Link href="/news/climate-education-workshop-reaches-rural-communities">
                      <Button variant="ghost" size="sm" className="p-0 h-auto text-green-600">
                        Read More <ArrowRight className="ml-1 h-3 w-3" />
                      </Button>
                    </Link>
                  </CardContent>
                </Card>

                <Card className="border-0 shadow-lg">
                  <CardContent className="p-6">
                    <div className="text-sm text-gray-500 mb-2">March 5, 2024</div>
                    <h4 className="font-semibold text-gray-900 mb-3">
                      New Partnership with Local Environmental Groups
                    </h4>
                    <p className="text-sm text-gray-600 mb-4">
                      ACEF announces strategic partnership with local NGOs for expanded conservation efforts...
                    </p>
                    <Link href="/news/acef-partners-with-local-ngos-for-expanded-conservation">
                      <Button variant="ghost" size="sm" className="p-0 h-auto text-green-600">
                        Read More <ArrowRight className="ml-1 h-3 w-3" />
                      </Button>
                    </Link>
                  </CardContent>
                </Card>

                <Card className="border-0 shadow-lg">
                  <CardContent className="p-6">
                    <div className="text-sm text-gray-500 mb-2">February 28, 2024</div>
                    <h4 className="font-semibold text-gray-900 mb-3">
                      Sustainable Agriculture Training Program Launch
                    </h4>
                    <p className="text-sm text-gray-600 mb-4">
                      New training program helps farmers adopt eco-friendly agricultural practices...
                    </p>
                    <Link href="/news/sustainable-agriculture-training-program-launch">
                      <Button variant="ghost" size="sm" className="p-0 h-auto text-green-600">
                        Read More <ArrowRight className="ml-1 h-3 w-3" />
                      </Button>
                    </Link>
                  </CardContent>
                </Card>
              </div>
            </div>

            <div className="text-center mt-12">
              <Link href="/news">
                <Button size="lg" variant="outline" className="border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white px-8 py-3 rounded-full">
                  View All News
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <Badge className="mb-4 bg-purple-100 text-purple-800">Testimonials</Badge>
              <h2 className="text-4xl md:text-5xl font-bold mb-6 text-gray-900">
                Voices from Our Community
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Hear from community members, volunteers, and partners about the impact of our environmental work
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <Card className="border-0 shadow-lg bg-white">
                <CardContent className="p-8">
                  <Quote className="h-8 w-8 text-green-600 mb-4" />
                  <p className="text-gray-600 mb-6 italic">
                    "ACEF's tree planting program has transformed our community. The air is cleaner,
                    and we've learned so much about environmental conservation."
                  </p>
                  <div className="flex items-center space-x-3">
                    <Avatar>
                      <AvatarFallback className="bg-green-100 text-green-800">MK</AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="font-medium text-gray-900">Marie Kone</div>
                      <div className="text-sm text-gray-600">Community Leader, Limbe</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-lg bg-white">
                <CardContent className="p-8">
                  <Quote className="h-8 w-8 text-blue-600 mb-4" />
                  <p className="text-gray-600 mb-6 italic">
                    "The climate education workshops opened my eyes to environmental issues.
                    Now I'm actively involved in conservation efforts in my village."
                  </p>
                  <div className="flex items-center space-x-3">
                    <Avatar>
                      <AvatarFallback className="bg-blue-100 text-blue-800">PT</AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="font-medium text-gray-900">Paul Tabi</div>
                      <div className="text-sm text-gray-600">Farmer & Volunteer</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-lg bg-white">
                <CardContent className="p-8">
                  <Quote className="h-8 w-8 text-purple-600 mb-4" />
                  <p className="text-gray-600 mb-6 italic">
                    "Working with ACEF has been incredibly rewarding. Their approach to community-based
                    conservation is exactly what Africa needs."
                  </p>
                  <div className="flex items-center space-x-3">
                    <Avatar>
                      <AvatarFallback className="bg-purple-100 text-purple-800">AK</AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="font-medium text-gray-900">Amina Kone</div>
                      <div className="text-sm text-gray-600">Environmental Scientist</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Call-to-Action Section */}
      <section className="py-20 bg-gradient-to-r from-green-600 via-green-700 to-blue-800 text-white relative overflow-hidden">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-4xl md:text-5xl font-bold mb-8">
              Join the Environmental Movement
            </h2>
            <p className="text-xl mb-12 opacity-90 leading-relaxed">
              Every action counts in the fight against climate change. Join us in our mission to protect
              Africa's environment and create a sustainable future for generations to come.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
              <div className="text-center">
                <div className="bg-white/20 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Heart className="h-8 w-8" />
                </div>
                <h3 className="text-xl font-semibold mb-2">Donate</h3>
                <p className="opacity-80">Support our environmental programs</p>
              </div>
              <div className="text-center">
                <div className="bg-white/20 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Users className="h-8 w-8" />
                </div>
                <h3 className="text-xl font-semibold mb-2">Volunteer</h3>
                <p className="opacity-80">Join our community initiatives</p>
              </div>
              <div className="text-center">
                <div className="bg-white/20 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Globe className="h-8 w-8" />
                </div>
                <h3 className="text-xl font-semibold mb-2">Spread Awareness</h3>
                <p className="opacity-80">Share our environmental message</p>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <Link href="/donate">
                <Button size="lg" className="bg-white text-green-800 hover:bg-gray-100 px-8 py-4 text-lg rounded-full shadow-2xl">
                  <Heart className="mr-3 h-6 w-6" />
                  Donate Now
                </Button>
              </Link>
              <Link href="/volunteer">
                <Button size="lg" variant="outline" className="border-2 border-white text-white hover:bg-white hover:text-green-800 px-8 py-4 text-lg rounded-full">
                  <Users className="mr-3 h-6 w-6" />
                  Become a Volunteer
                </Button>
              </Link>
              <Link href="/contact">
                <Button size="lg" variant="outline" className="border-2 border-white text-white hover:bg-white hover:text-green-800 px-8 py-4 text-lg rounded-full">
                  <ArrowRight className="mr-3 h-6 w-6" />
                  Contact Us
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Partners Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-12">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Our Partners & Supporters</h3>
              <p className="text-gray-600">Working together for a sustainable future</p>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8 items-center opacity-60">
              {/* Placeholder partner logos */}
              <div className="bg-gray-100 h-16 rounded-lg flex items-center justify-center">
                <span className="text-gray-400 font-medium">Partner 1</span>
              </div>
              <div className="bg-gray-100 h-16 rounded-lg flex items-center justify-center">
                <span className="text-gray-400 font-medium">Partner 2</span>
              </div>
              <div className="bg-gray-100 h-16 rounded-lg flex items-center justify-center">
                <span className="text-gray-400 font-medium">Partner 3</span>
              </div>
              <div className="bg-gray-100 h-16 rounded-lg flex items-center justify-center">
                <span className="text-gray-400 font-medium">Partner 4</span>
              </div>
              <div className="bg-gray-100 h-16 rounded-lg flex items-center justify-center">
                <span className="text-gray-400 font-medium">Partner 5</span>
              </div>
              <div className="bg-gray-100 h-16 rounded-lg flex items-center justify-center">
                <span className="text-gray-400 font-medium">Partner 6</span>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
