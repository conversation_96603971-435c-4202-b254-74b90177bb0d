# Basic Package Prototype - Sitemap & UX Guidelines
*Complete Blueprint for NGO Website Prototype Development*

---

## 🗺️ **DETAILED SITEMAP**

### **MAIN NAVIGATION STRUCTURE**
```
HOME PAGE
├── ABOUT US
├── PROGRAMS & PROJECTS
│   ├── Program Detail Page (Template)
│   └── Project Detail Page (Template)
├── BLOG & NEWS
│   ├── Blog Article Page (Template)
│   └── News Article Page (Template)
├── ARTICLES & RESEARCH
│   └── Research Paper Detail Page (Template)
├── EVENTS
│   └── Event Detail Page (Template)
├── VOLUNTEERING
├── CONTACT
└── FOOTER LINKS
    ├── Privacy Policy
    └── Terms of Use
```

### **PAGE HIERARCHY & RELATIONSHIPS**
- **Static Pages**: 8 main pages + 2 legal pages = 10 pages ✅
- **Dynamic Templates**: 5 content type templates (not counted in static page limit)
- **CMS-Generated Content**: All dynamic content managed through admin dashboard

---

## 📄 **DETAILED PAGE SPECIFICATIONS**

### **1. HOMEPAGE**
**Purpose**: First impression, mission communication, clear navigation

**Essential Sections**:
1. **Header Navigation Bar**
   - Logo (left side)
   - Main menu (center/right): Home | About Us | Programs & Projects | Blog & News | Articles & Research | Events | Volunteering | Contact
   - Language selector (if bilingual) - Basic: Single language only
   - Search icon (basic search functionality)

2. **Hero Section**
   - Large background image/video area
   - Overlay with mission statement (2-3 sentences max)
   - Primary CTA button: "Donate Now"
   - Secondary CTA button: "Join Us" or "Get Involved"

3. **Mission Statement Section**
   - Headline: "Our Mission"
   - 2-3 paragraph description
   - Key impact numbers (if available): "X people helped", "X projects completed"

4. **Featured Programs Section**
   - Headline: "Our Programs"
   - 3-card layout showing latest/featured programs
   - Each card: Image, title, brief description, "Learn More" button
   - "View All Programs" button

5. **Latest News Section**
   - Headline: "Latest News & Updates"
   - 2-3 recent blog posts/news items
   - Each item: Image, title, date, excerpt, "Read More" button
   - "View All News" button

6. **Call-to-Action Section**
   - Background color/pattern
   - "Get Involved" headline
   - Brief text encouraging participation
   - Two buttons: "Volunteer" and "Contact Us"

7. **Footer**
   - NGO logo and brief description
   - Contact information (address, phone, email)
   - Social media links
   - Quick links (Privacy Policy, Terms of Use)
   - Newsletter signup form
   - Copyright notice

### **2. ABOUT US PAGE**
**Purpose**: Build trust, showcase team, communicate values

**Content Structure**:
1. **Page Header**
   - Page title: "About Us"
   - Breadcrumb navigation: Home > About Us

2. **NGO Profile Section**
   - Organization name and founding year
   - Mission statement (detailed version)
   - Vision statement
   - Core values (4-6 key values)

3. **Our History Section**
   - Timeline format or narrative
   - Key milestones and achievements
   - Growth and impact over time

4. **Our Team Section**
   - Team member cards (4-8 people)
   - Each card: Photo, name, title, brief bio
   - "Meet the full team" expandable section

5. **Impact Numbers Section**
   - Visual statistics: beneficiaries reached, projects completed, years of service
   - Use icons and large numbers for visual impact

### **3. PROGRAMS & PROJECTS PAGE**
**Purpose**: Showcase all programs with easy navigation and filtering

**Layout & Functionality**:
1. **Page Header**
   - Page title: "Programs & Projects"
   - Breadcrumb: Home > Programs & Projects

2. **Filter/Category Bar**
   - Basic filter options: All | Programs | Projects | Active | Completed
   - Search bar for keyword search

3. **Content Grid**
   - Card-based layout (3 columns on desktop, 1-2 on mobile)
   - Each card contains:
     - Featured image
     - Program/Project title
     - Category tag
     - Brief description (2-3 lines)
     - Status indicator (Active/Completed)
     - "Learn More" button

4. **Pagination**
   - Show 9-12 items per page
   - Simple pagination controls

**Individual Program/Project Detail Template**:
- **Hero Section**: Large image, title, status
- **Overview**: Detailed description, objectives
- **Impact Metrics**: Numbers, statistics, beneficiaries
- **Timeline**: Key milestones or project phases
- **Gallery**: 4-6 relevant images
- **Related Programs**: 2-3 related items
- **CTA**: "Support This Program" or "Get Involved"

### **4. BLOG & NEWS PAGE**
**Purpose**: Share updates, stories, and organizational news

**Main Page Layout**:
1. **Page Header**
   - Title: "Blog & News"
   - Breadcrumb: Home > Blog & News

2. **Featured Post**
   - Large card with most recent/important post
   - Large image, title, date, author, excerpt
   - "Read Full Article" button

3. **Content Categories**
   - Filter tabs: All | News | Stories | Updates | Announcements

4. **Article Grid**
   - 2-column layout (1 column on mobile)
   - Each article card:
     - Thumbnail image
     - Category tag
     - Title
     - Date and author
     - Excerpt (2-3 lines)
     - "Read More" button

**Individual Article Template**:
- **Article Header**: Title, date, author, category
- **Featured Image**: Large header image
- **Article Content**: Full text with basic formatting
- **Social Sharing**: Share buttons for social media
- **Related Articles**: 2-3 related posts
- **Comments Section**: Basic comment form (optional)

### **5. ARTICLES & RESEARCH PAGE**
**Purpose**: Publish research documents and academic content

**Main Page Layout**:
1. **Page Header**
   - Title: "Articles & Research Papers"
   - Breadcrumb: Home > Articles & Research

2. **Document Categories**
   - Filter options: All | Research Papers | Policy Briefs | Reports | Studies

3. **Document Listing**
   - List format (not grid)
   - Each entry:
     - Document title
     - Author(s)
     - Publication date
     - Category tag
     - Brief abstract (2-3 lines)
     - Download PDF button
     - "View Details" link

**Individual Research Document Template**:
- **Document Header**: Title, authors, publication date
- **Abstract**: Full document summary
- **Document Details**: Category, keywords, file size
- **Download Section**: PDF download button
- **Citation Information**: Proper citation format
- **Related Documents**: Similar research papers

### **6. EVENTS PAGE**
**Purpose**: List upcoming and past events with basic information

**Main Page Layout**:
1. **Page Header**
   - Title: "Events"
   - Breadcrumb: Home > Events

2. **Event Filters**
   - Filter tabs: Upcoming | Past | All
   - Category filters: Workshops | Meetings | Fundraisers | Community Events

3. **Event Listing**
   - Calendar-style or list view
   - Each event card:
     - Event image
     - Title
     - Date and time
     - Location
     - Brief description
     - "View Details" button

**Individual Event Template**:
- **Event Header**: Title, date, time, location
- **Event Description**: Full details about the event
- **Location Information**: Address, map (if possible)
- **Event Gallery**: Photos from similar past events
- **Contact Information**: For inquiries
- **Note**: "Registration through phone/email contact" (no online registration in Basic package)

### **7. VOLUNTEERING PAGE**
**Purpose**: Attract and collect volunteer applications

**Page Structure**:
1. **Page Header**
   - Title: "Volunteer With Us"
   - Breadcrumb: Home > Volunteering

2. **Why Volunteer Section**
   - Compelling headline
   - Benefits of volunteering
   - Impact stories or testimonials

3. **Volunteer Opportunities**
   - Different volunteer roles available
   - Time commitments
   - Skills needed
   - Contact information for each opportunity

4. **Volunteer Application Form**
   - Personal information fields
   - Skills and interests
   - Availability
   - Experience
   - Motivation/Why they want to volunteer
   - Submit button

5. **Volunteer Testimonials**
   - 2-3 testimonials from current volunteers
   - Photos and quotes

### **8. CONTACT PAGE**
**Purpose**: Provide multiple ways to reach the organization

**Page Layout**:
1. **Page Header**
   - Title: "Contact Us"
   - Breadcrumb: Home > Contact

2. **Contact Information Section**
   - Office address
   - Phone numbers
   - Email addresses
   - Office hours
   - Map location (embedded Google Maps)

3. **Contact Form**
   - Name, email, phone (required fields)
   - Subject dropdown (General Inquiry, Partnership, Media, Other)
   - Message field
   - Submit button

4. **Other Ways to Reach Us**
   - Social media links
   - Newsletter signup
   - Emergency contact information

### **9. PRIVACY POLICY PAGE**
**Purpose**: Legal compliance and transparency

**Content Structure**:
- Standard privacy policy template
- Information collection practices
- Data usage and protection
- Contact information for privacy concerns

### **10. TERMS OF USE PAGE**
**Purpose**: Legal protection and user guidelines

**Content Structure**:
- Website usage terms
- Intellectual property rights
- User responsibilities
- Limitation of liability

---

## 🎨 **DESIGN GUIDELINES**

### **Color Palette Recommendations**
- **Primary Color**: Choose one main brand color (blue, green, or purple work well for NGOs)
- **Secondary Color**: Complementary accent color
- **Neutral Colors**: White, light gray, dark gray for text and backgrounds
- **Status Colors**: Green for success, red for urgent, orange for warnings

### **Typography Guidelines**
- **Headers**: Bold, clean sans-serif font (like Open Sans, Roboto, or Lato)
- **Body Text**: Readable sans-serif, minimum 16px size
- **Hierarchy**: Clear distinction between H1, H2, H3, and body text

### **Layout Principles**
- **Grid System**: Use 12-column grid for consistency
- **Spacing**: Consistent margins and padding throughout
- **Mobile-First**: Design for mobile, then adapt for desktop
- **Accessibility**: High contrast, clear navigation, alt text for images

### **Component Standards**
- **Buttons**: 2 styles - Primary (filled) and Secondary (outlined)
- **Cards**: Consistent card design for programs, news, events
- **Forms**: Clear labels, proper spacing, validation messages
- **Navigation**: Consistent across all pages

---

## 🖥️ **TECHNICAL SPECIFICATIONS**

### **Responsive Breakpoints**
- **Mobile**: 320px - 767px (single column layout)
- **Tablet**: 768px - 1023px (2-column layout)
- **Desktop**: 1024px+ (3-column layout where applicable)

### **Performance Requirements**
- **Page Load Speed**: Under 3 seconds
- **Image Optimization**: All images compressed and properly sized
- **Mobile Performance**: Smooth scrolling and touch interactions

### **Basic SEO Elements**
- **Meta Titles**: Unique for each page, 50-60 characters
- **Meta Descriptions**: Compelling descriptions, 150-160 characters
- **Header Tags**: Proper H1, H2, H3 hierarchy
- **Alt Text**: Descriptive alt text for all images

---

## 📱 **CMS DASHBOARD MOCKUP SPECIFICATIONS**

### **Dashboard Homepage**
- **Quick Stats**: Total programs, recent blog posts, upcoming events
- **Recent Activity**: Latest content additions/updates
- **Quick Actions**: Add new program, create blog post, add event

### **Content Management Sections**
1. **Programs & Projects Manager**
   - List view of all programs
   - Add/Edit/Delete functions
   - Basic text editor for content

2. **Blog & News Manager**
   - List of all blog posts
   - Category management
   - Publishing controls

3. **Articles & Research Manager**
   - Document upload interface
   - Metadata management
   - Publication status

4. **Events Manager**
   - Calendar view
   - Event creation form
   - Event status management

5. **Media Library**
   - Image upload interface
   - File organization
   - Basic image editing

---

## 🎯 **PROTOTYPE INTERACTION GUIDELINES**

### **Navigation Behavior**
- **Main Menu**: Hover effects on desktop, tap to expand on mobile
- **Breadcrumbs**: Clickable navigation path
- **Search**: Functional search bar with results page
- **Pagination**: Working pagination on listing pages

### **Form Interactions**
- **Contact Form**: Validation messages and success confirmation
- **Volunteer Form**: Progressive disclosure for additional fields
- **Newsletter Signup**: Success/error states

### **Content Interactions**
- **Image Galleries**: Lightbox functionality for larger images
- **Read More**: Expandable content sections
- **Filtering**: Functional filters on Programs and Events pages

### **Mobile-Specific Interactions**
- **Touch-Friendly**: Minimum 44px touch targets
- **Swipe Navigation**: For image galleries
- **Collapsible Menus**: Mobile navigation drawer

---

## 📋 **CONTENT GUIDELINES FOR PROTOTYPE**

### **Sample Content Requirements**
- **NGO Name**: Use "Hope Foundation Cameroon" as example
- **Mission**: Focus on education and community development
- **Programs**: 6-8 sample programs (education, health, environment, women empowerment)
- **Blog Posts**: 8-10 sample articles with varied content
- **Events**: 4-6 upcoming and past events
- **Team Members**: 6-8 team member profiles
- **Research Papers**: 4-5 sample documents

### **Image Requirements**
- **High Quality**: Professional-looking stock photos
- **Relevant Content**: African/Cameroonian context where possible
- **Consistent Style**: Similar color tone and style
- **Optimized Sizes**: Properly sized for web use

### **Text Content**
- **Realistic Length**: Actual paragraph lengths, not Lorem Ipsum
- **Compelling Copy**: Engaging and mission-focused content
- **Varied Content**: Different types of programs and activities

---

## ✅ **FINAL DELIVERABLE SPECIFICATIONS**

### **Prototype Requirements**
1. **Interactive Mockup**: Clickable prototype showing all main pages
2. **Responsive Design**: Show mobile and desktop versions
3. **Navigation Flow**: Complete user journey through all pages
4. **Form Interactions**: Working contact and volunteer forms
5. **CMS Demo**: Simple dashboard showing content management

### **Documentation**
1. **Style Guide**: Colors, fonts, button styles, spacing
2. **Component Library**: Reusable UI elements
3. **User Flow Diagram**: How users navigate the site
4. **Technical Notes**: Any specific implementation requirements

### **Presentation Format**
- **Interactive Prototype**: Using Figma, Adobe XD, or similar tool
- **Presentation Mode**: Easy to demo to clients
- **Export Options**: Ability to share links or export images
- **Comments/Notes**: Annotations explaining key features

---

**This prototype will serve as a comprehensive visual representation of the Basic Package, allowing NGOs to understand exactly what they'll receive and how their website will function.**