import Image from "next/image";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  ArrowRight, 
  Calendar, 
  Clock, 
  User, 
  Tag, 
  Eye,
  MessageCircle,
  Share2,
  ArrowLeft,
  TrendingUp,
  MapPin
} from "lucide-react";

interface NewsArticlePageProps {
  params: {
    slug: string;
  };
}

export default function NewsArticlePage({ params }: NewsArticlePageProps) {
  // In a real app, you would fetch the news article data based on the slug
  const newsArticle = {
    title: "ACEF Surpasses 500 Trees Planted Across 15 Communities in Cameroon",
    excerpt: "In a remarkable achievement for environmental conservation, ACEF has successfully planted over 500 trees across 15 communities in Cameroon.",
    content: `
      <p><strong>Limbe, Cameroon</strong> - The Africa Climate and Environment Foundation (ACEF) has reached a significant milestone in its reforestation efforts, successfully planting over 500 trees across 15 communities in Cameroon's South West Region. This achievement represents a major step forward in the organization's mission to combat deforestation and climate change in Central Africa.</p>

      <p>The tree planting initiative, which began in early 2024, has engaged over 500 community volunteers and has focused on planting native species that are well-adapted to the local climate and ecosystem. The project has not only contributed to environmental restoration but has also strengthened community bonds and created new opportunities for environmental education.</p>

      <h2>Community Engagement at the Heart of Success</h2>
      <p>"What makes this achievement truly special is the level of community engagement we've witnessed," said Dr. Jane Doe, Executive Director of ACEF. "These aren't just numbers on a report – each tree represents a community member who has taken ownership of environmental conservation in their area."</p>

      <p>The initiative has involved extensive community consultations to identify the most suitable locations for tree planting and to select species that would provide maximum environmental and economic benefits to local communities. Priority has been given to areas that have experienced significant deforestation and where tree cover restoration could help prevent soil erosion and improve water retention.</p>

      <h2>Environmental and Economic Impact</h2>
      <p>The 500+ trees planted are expected to sequester approximately 125 tons of CO2 over their lifetime, contributing significantly to climate change mitigation efforts in the region. Additionally, many of the planted species will provide economic benefits to communities through sustainable harvesting of fruits, nuts, and other forest products.</p>

      <p>The project has also created temporary employment opportunities for community members involved in site preparation, planting, and ongoing maintenance of the young trees. ACEF has committed to supporting the communities with technical assistance and resources for tree care for the next three years to ensure high survival rates.</p>

      <h2>Expanding the Initiative</h2>
      <p>Building on this success, ACEF has announced plans to expand the tree planting initiative to reach 1,000 trees by the end of 2024. The organization is currently seeking partnerships with local schools, women's groups, and youth organizations to broaden community participation and create lasting environmental stewardship.</p>

      <p>"This is just the beginning," explained Michael Smith, ACEF's Program Manager. "We're not just planting trees; we're planting the seeds of environmental consciousness that will grow and spread throughout these communities for generations to come."</p>

      <h2>Recognition and Support</h2>
      <p>The achievement has garnered attention from environmental organizations across Central Africa, with several expressing interest in replicating ACEF's community-centered approach. The Cameroon Ministry of Environment has also commended ACEF's efforts and has indicated support for scaling up the initiative to other regions.</p>

      <p>Local community leaders have praised the initiative for its inclusive approach and tangible benefits. "Before ACEF came, we knew our forests were disappearing, but we didn't know what to do about it," said Marie Kone, a community leader from one of the participating villages. "Now we have the knowledge and tools to be part of the solution."</p>

      <h2>Looking Ahead</h2>
      <p>As ACEF celebrates this milestone, the organization remains focused on its broader mission of environmental conservation and climate action across Africa. Plans are underway to launch similar initiatives in other countries, with preliminary discussions already taking place with partners in Nigeria and Ghana.</p>

      <p>The success of the tree planting initiative demonstrates the power of community-led conservation efforts and provides a model for other organizations working on environmental restoration in Africa. As climate change continues to pose significant challenges to the continent, initiatives like this offer hope and practical solutions for building resilience and protecting natural resources for future generations.</p>
    `,
    author: {
      name: "ACEF Communications Team",
      role: "Press Release",
      avatar: "AC"
    },
    publishedAt: "March 18, 2024",
    readTime: "4 min read",
    category: "Achievement",
    location: "Limbe, Cameroon",
    tags: ["Reforestation", "Community Engagement", "Milestone", "Conservation"],
    views: 2134,
    comments: 45,
    image: "https://images.unsplash.com/photo-1569163139394-de4e4f43e4e3?q=80&w=2070&auto=format&fit=crop",
    isBreaking: true,
    priority: "high"
  };

  return (
    <div className="flex flex-col">
      {/* Breaking News Alert */}
      {newsArticle.isBreaking && (
        <Alert className="rounded-none border-0 bg-red-600 text-white">
          <TrendingUp className="h-4 w-4" />
          <AlertDescription>
            <strong>Breaking News:</strong> This is a developing story with significant environmental impact.
          </AlertDescription>
        </Alert>
      )}

      {/* Breadcrumb */}
      <section className="py-6 bg-gray-50 border-b">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink href="/">Home</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbLink href="/news">News</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbPage>{newsArticle.title.substring(0, 50)}...</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </div>
      </section>

      {/* Article Header */}
      <section className="py-12 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <Button variant="ghost" className="mb-6 p-0 h-auto text-gray-600 hover:text-green-600">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to News
            </Button>

            <div className="mb-8">
              <div className="flex items-center space-x-3 mb-4">
                <Badge className="bg-red-100 text-red-800">{newsArticle.category}</Badge>
                {newsArticle.isBreaking && (
                  <Badge className="bg-red-500 text-white animate-pulse">Breaking</Badge>
                )}
                <Badge variant="outline" className="text-gray-600">
                  <MapPin className="h-3 w-3 mr-1" />
                  {newsArticle.location}
                </Badge>
              </div>
              
              <h1 className="text-3xl md:text-5xl font-bold mb-6 text-gray-900 leading-tight">
                {newsArticle.title}
              </h1>
              
              <p className="text-xl text-gray-600 leading-relaxed mb-8">
                {newsArticle.excerpt}
              </p>

              {/* Article Meta */}
              <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-6">
                <div className="flex items-center space-x-4">
                  <Avatar className="h-12 w-12">
                    <AvatarFallback className="bg-blue-100 text-blue-800 font-semibold">
                      {newsArticle.author.avatar}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-semibold text-gray-900">{newsArticle.author.name}</div>
                    <div className="text-sm text-gray-600">{newsArticle.author.role}</div>
                  </div>
                </div>

                <div className="flex items-center space-x-6 text-sm text-gray-500">
                  <div className="flex items-center space-x-1">
                    <Calendar className="h-4 w-4" />
                    <span>{newsArticle.publishedAt}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Clock className="h-4 w-4" />
                    <span>{newsArticle.readTime}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Eye className="h-4 w-4" />
                    <span>{newsArticle.views} views</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Featured Image */}
            <div className="aspect-video relative overflow-hidden rounded-2xl shadow-2xl mb-12">
              <Image
                src={newsArticle.image}
                alt={newsArticle.title}
                fill
                className="object-cover"
              />
              <div className="absolute bottom-4 left-4 bg-black/70 text-white px-3 py-1 rounded text-sm">
                ACEF tree planting initiative in Cameroon
              </div>
            </div>

            {/* Article Actions */}
            <div className="flex items-center justify-between mb-12 p-6 bg-gray-50 rounded-xl">
              <div className="flex items-center space-x-4">
                <Button variant="outline" size="sm" className="rounded-full">
                  <MessageCircle className="h-4 w-4 mr-2" />
                  {newsArticle.comments} Comments
                </Button>
                <Button variant="outline" size="sm" className="rounded-full">
                  <Eye className="h-4 w-4 mr-2" />
                  {newsArticle.views} Views
                </Button>
              </div>
              <Button variant="outline" size="sm" className="rounded-full">
                <Share2 className="h-4 w-4 mr-2" />
                Share Article
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Article Content */}
      <section className="py-12 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div 
              className="prose prose-lg max-w-none prose-headings:text-gray-900 prose-p:text-gray-700 prose-p:leading-relaxed prose-a:text-green-600 prose-strong:text-gray-900"
              dangerouslySetInnerHTML={{ __html: newsArticle.content }}
            />

            {/* Tags */}
            <div className="mt-12 pt-8 border-t">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Tags</h3>
              <div className="flex flex-wrap gap-2">
                {newsArticle.tags.map((tag, index) => (
                  <Badge key={index} variant="secondary" className="text-sm">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>

            {/* Contact Information */}
            <Card className="mt-12 border-0 shadow-lg bg-blue-50">
              <CardContent className="p-8">
                <h3 className="text-xl font-bold text-gray-900 mb-4">Media Contact</h3>
                <div className="space-y-2 text-gray-700">
                  <p><strong>Organization:</strong> Africa Climate and Environment Foundation (ACEF)</p>
                  <p><strong>Email:</strong> <EMAIL></p>
                  <p><strong>Phone:</strong> +237 XXX XXX XXX</p>
                  <p><strong>Location:</strong> Limbe, Cameroon</p>
                </div>
                <Button className="mt-4 bg-blue-600 hover:bg-blue-700">
                  Contact Media Team
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Related News */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl font-bold text-gray-900 mb-12 text-center">Related News</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {/* Related News 1 */}
              <Card className="border-0 shadow-lg hover:shadow-xl transition-all bg-white overflow-hidden group">
                <div className="aspect-video relative overflow-hidden">
                  <Image
                    src="https://images.unsplash.com/photo-1558618666-fcd25c85cd64?q=80&w=2070&auto=format&fit=crop"
                    alt="Related news"
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                  <Badge className="absolute top-4 left-4 bg-blue-100 text-blue-800">Education</Badge>
                </div>
                <CardContent className="p-6">
                  <div className="text-sm text-gray-500 mb-2">March 15, 2024</div>
                  <h3 className="text-lg font-bold mb-2 text-gray-900 group-hover:text-green-600 transition-colors">
                    Climate Education Program Reaches 300+ Students
                  </h3>
                  <p className="text-gray-600 text-sm mb-4">
                    ACEF's educational initiative expands to rural schools across Cameroon...
                  </p>
                  <Button variant="ghost" size="sm" className="p-0 h-auto text-green-600">
                    Read More
                    <ArrowRight className="ml-1 h-3 w-3" />
                  </Button>
                </CardContent>
              </Card>

              {/* Related News 2 */}
              <Card className="border-0 shadow-lg hover:shadow-xl transition-all bg-white overflow-hidden group">
                <div className="aspect-video relative overflow-hidden">
                  <Image
                    src="https://images.unsplash.com/photo-1593113598332-cd288d649433?q=80&w=2070&auto=format&fit=crop"
                    alt="Related news"
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                  <Badge className="absolute top-4 left-4 bg-green-100 text-green-800">Partnership</Badge>
                </div>
                <CardContent className="p-6">
                  <div className="text-sm text-gray-500 mb-2">March 12, 2024</div>
                  <h3 className="text-lg font-bold mb-2 text-gray-900 group-hover:text-green-600 transition-colors">
                    New Partnership Expands Conservation Efforts
                  </h3>
                  <p className="text-gray-600 text-sm mb-4">
                    Strategic collaboration with local NGOs strengthens environmental impact...
                  </p>
                  <Button variant="ghost" size="sm" className="p-0 h-auto text-green-600">
                    Read More
                    <ArrowRight className="ml-1 h-3 w-3" />
                  </Button>
                </CardContent>
              </Card>

              {/* Related News 3 */}
              <Card className="border-0 shadow-lg hover:shadow-xl transition-all bg-white overflow-hidden group">
                <div className="aspect-video relative overflow-hidden">
                  <Image
                    src="https://images.unsplash.com/photo-1473773508845-188df298d2d1?q=80&w=2074&auto=format&fit=crop"
                    alt="Related news"
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                  <Badge className="absolute top-4 left-4 bg-orange-100 text-orange-800">Agriculture</Badge>
                </div>
                <CardContent className="p-6">
                  <div className="text-sm text-gray-500 mb-2">March 10, 2024</div>
                  <h3 className="text-lg font-bold mb-2 text-gray-900 group-hover:text-green-600 transition-colors">
                    Sustainable Agriculture Training Success
                  </h3>
                  <p className="text-gray-600 text-sm mb-4">
                    150+ farmers benefit from eco-friendly farming techniques training...
                  </p>
                  <Button variant="ghost" size="sm" className="p-0 h-auto text-green-600">
                    Read More
                    <ArrowRight className="ml-1 h-3 w-3" />
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
