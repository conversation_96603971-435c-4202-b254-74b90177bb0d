import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  ArrowRight, 
  Search, 
  Download, 
  FileText, 
  Calendar, 
  User, 
  Tag, 
  Eye,
  Quote,
  BookOpen,
  Award,
  Filter,
  SortAsc,
  Share2,
  Bookmark
} from "lucide-react";

export default function ResearchPage() {
  return (
    <div className="flex flex-col">
      {/* <PERSON> Header */}
      <section className="py-16 bg-gradient-to-r from-blue-50 to-green-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <Breadcrumb className="justify-center mb-6">
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink href="/">Home</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbPage>Articles & Research</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
            <h1 className="text-4xl md:text-6xl font-bold mb-6 text-gray-900">
              Research Publications
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Explore our collection of research papers, policy briefs, and scientific publications 
              on environmental conservation and climate change in Africa
            </p>
          </div>
        </div>
      </section>

      {/* Search and Filter Section */}
      <section className="py-8 bg-white border-b">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
              <div className="flex items-center space-x-4 w-full lg:w-auto">
                <div className="relative flex-1 lg:w-96">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input 
                    placeholder="Search research papers, authors, keywords..." 
                    className="pl-10 rounded-lg border-gray-300"
                  />
                </div>
                <Button variant="outline" className="rounded-lg">
                  <Filter className="h-4 w-4 mr-2" />
                  Filter
                </Button>
                <Button variant="outline" className="rounded-lg">
                  <SortAsc className="h-4 w-4 mr-2" />
                  Sort
                </Button>
              </div>
              <div className="flex items-center space-x-2">
                <Badge variant="outline">All</Badge>
                <Badge variant="outline">Research Papers</Badge>
                <Badge variant="outline">Policy Briefs</Badge>
                <Badge variant="outline">Reports</Badge>
                <Badge variant="outline">Studies</Badge>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Research Statistics */}
      <section className="py-8 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600 mb-1">25+</div>
                <div className="text-sm text-gray-600">Research Papers</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600 mb-1">15+</div>
                <div className="text-sm text-gray-600">Policy Briefs</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-600 mb-1">10+</div>
                <div className="text-sm text-gray-600">Technical Reports</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-orange-600 mb-1">5,000+</div>
                <div className="text-sm text-gray-600">Total Downloads</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Research */}
      <section className="py-12 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="mb-8">
              <Badge className="bg-blue-100 text-blue-800 mb-4">Featured Research</Badge>
              <h2 className="text-3xl font-bold text-gray-900">Latest Publications</h2>
            </div>
            
            <Card className="border-0 shadow-xl overflow-hidden mb-12">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-0">
                <div className="lg:col-span-2 p-8">
                  <div className="flex items-center space-x-4 mb-4">
                    <Badge className="bg-green-100 text-green-800">Research Paper</Badge>
                    <Badge variant="outline">Peer Reviewed</Badge>
                    <div className="flex items-center space-x-1 text-sm text-gray-500">
                      <Calendar className="h-4 w-4" />
                      <span>Published March 2024</span>
                    </div>
                  </div>
                  
                  <h3 className="text-2xl md:text-3xl font-bold mb-4 text-gray-900 leading-tight">
                    Climate Change Adaptation Strategies for Smallholder Farmers in Cameroon: 
                    A Comprehensive Analysis
                  </h3>
                  
                  <div className="flex items-center space-x-4 mb-6">
                    <div className="flex items-center space-x-2">
                      <Avatar className="h-10 w-10">
                        <AvatarFallback className="bg-green-100 text-green-800">JD</AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-semibold text-gray-900">Dr. Jane Doe</div>
                        <div className="text-sm text-gray-600">Lead Researcher, ACEF</div>
                      </div>
                    </div>
                    <div className="text-gray-400">•</div>
                    <div className="flex items-center space-x-2">
                      <Avatar className="h-10 w-10">
                        <AvatarFallback className="bg-blue-100 text-blue-800">MS</AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-semibold text-gray-900">Michael Smith</div>
                        <div className="text-sm text-gray-600">Co-author</div>
                      </div>
                    </div>
                  </div>
                  
                  <p className="text-gray-600 mb-6 leading-relaxed">
                    This comprehensive study examines the climate change adaptation strategies employed by 
                    smallholder farmers in Cameroon's agricultural regions. Through extensive field research 
                    and data analysis, we identify key challenges and propose sustainable solutions for 
                    climate-resilient agriculture in Central Africa.
                  </p>
                  
                  <div className="flex items-center space-x-4 mb-6">
                    <div className="flex items-center space-x-1 text-sm text-gray-500">
                      <Eye className="h-4 w-4" />
                      <span>2,341 views</span>
                    </div>
                    <div className="flex items-center space-x-1 text-sm text-gray-500">
                      <Download className="h-4 w-4" />
                      <span>456 downloads</span>
                    </div>
                    <div className="flex items-center space-x-1 text-sm text-gray-500">
                      <Quote className="h-4 w-4" />
                      <span>23 citations</span>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    <Link href="/research/climate-change-adaptation-strategies-smallholder-farmers-cameroon">
                      <Button className="bg-green-600 hover:bg-green-700">
                        <Download className="h-4 w-4 mr-2" />
                        Download PDF
                      </Button>
                    </Link>
                    <Link href="/research/climate-change-adaptation-strategies-smallholder-farmers-cameroon">
                      <Button variant="outline">
                        <Eye className="h-4 w-4 mr-2" />
                        View Abstract
                      </Button>
                    </Link>
                    <Button variant="outline" size="icon">
                      <Share2 className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="icon">
                      <Bookmark className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                
                <div className="bg-gray-50 p-8 flex flex-col justify-center">
                  <div className="space-y-6">
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Publication Details</h4>
                      <div className="space-y-2 text-sm text-gray-600">
                        <div><strong>Journal:</strong> African Environmental Research</div>
                        <div><strong>Volume:</strong> 15, Issue 3</div>
                        <div><strong>Pages:</strong> 45-72</div>
                        <div><strong>DOI:</strong> 10.1234/aer.2024.003</div>
                      </div>
                    </div>
                    
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Keywords</h4>
                      <div className="flex flex-wrap gap-2">
                        <Badge variant="secondary" className="text-xs">Climate Change</Badge>
                        <Badge variant="secondary" className="text-xs">Agriculture</Badge>
                        <Badge variant="secondary" className="text-xs">Adaptation</Badge>
                        <Badge variant="secondary" className="text-xs">Cameroon</Badge>
                        <Badge variant="secondary" className="text-xs">Sustainability</Badge>
                      </div>
                    </div>
                    
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">File Information</h4>
                      <div className="space-y-2 text-sm text-gray-600">
                        <div><strong>Format:</strong> PDF</div>
                        <div><strong>Size:</strong> 2.4 MB</div>
                        <div><strong>Pages:</strong> 28</div>
                        <div><strong>Language:</strong> English</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </section>

      {/* Research Papers List */}
      <section className="py-12 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="flex items-center justify-between mb-8">
              <h2 className="text-3xl font-bold text-gray-900">All Research Papers</h2>
              <div className="text-sm text-gray-600">Showing 1-10 of 25 results</div>
            </div>

            <div className="space-y-6">
              {/* Research Paper Item 1 */}
              <Card className="border-0 shadow-lg bg-white hover:shadow-xl transition-all">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-3">
                        <Badge className="bg-blue-100 text-blue-800">Policy Brief</Badge>
                        <div className="flex items-center space-x-1 text-sm text-gray-500">
                          <Calendar className="h-4 w-4" />
                          <span>February 2024</span>
                        </div>
                        <div className="flex items-center space-x-1 text-sm text-gray-500">
                          <FileText className="h-4 w-4" />
                          <span>12 pages</span>
                        </div>
                      </div>
                      
                      <h3 className="text-xl font-bold text-gray-900 mb-2 hover:text-green-600 cursor-pointer transition-colors">
                        Forest Conservation Policies in Central Africa: Current Status and Recommendations
                      </h3>
                      
                      <p className="text-gray-600 mb-4 leading-relaxed">
                        An analysis of current forest conservation policies across Central African countries, 
                        with specific focus on implementation challenges and policy recommendations for improved 
                        forest management and biodiversity protection.
                      </p>
                      
                      <div className="flex items-center space-x-4 mb-4">
                        <div className="flex items-center space-x-2">
                          <Avatar className="h-8 w-8">
                            <AvatarFallback className="bg-blue-100 text-blue-800 text-xs">AK</AvatarFallback>
                          </Avatar>
                          <span className="text-sm text-gray-600">Amina Kone</span>
                        </div>
                        <div className="text-gray-400">•</div>
                        <div className="flex items-center space-x-1 text-sm text-gray-500">
                          <Eye className="h-4 w-4" />
                          <span>1,234 views</span>
                        </div>
                        <div className="flex items-center space-x-1 text-sm text-gray-500">
                          <Download className="h-4 w-4" />
                          <span>234 downloads</span>
                        </div>
                      </div>
                      
                      <div className="flex flex-wrap gap-2">
                        <Badge variant="secondary" className="text-xs">Forest Conservation</Badge>
                        <Badge variant="secondary" className="text-xs">Policy</Badge>
                        <Badge variant="secondary" className="text-xs">Central Africa</Badge>
                        <Badge variant="secondary" className="text-xs">Biodiversity</Badge>
                      </div>
                    </div>
                    
                    <div className="ml-6 flex flex-col space-y-2">
                      <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                        <Download className="h-4 w-4 mr-2" />
                        Download
                      </Button>
                      <Button size="sm" variant="outline">
                        <Eye className="h-4 w-4 mr-2" />
                        Preview
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Research Paper Item 2 */}
              <Card className="border-0 shadow-lg bg-white hover:shadow-xl transition-all">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-3">
                        <Badge className="bg-green-100 text-green-800">Research Paper</Badge>
                        <Badge variant="outline">Peer Reviewed</Badge>
                        <div className="flex items-center space-x-1 text-sm text-gray-500">
                          <Calendar className="h-4 w-4" />
                          <span>January 2024</span>
                        </div>
                        <div className="flex items-center space-x-1 text-sm text-gray-500">
                          <FileText className="h-4 w-4" />
                          <span>24 pages</span>
                        </div>
                      </div>
                      
                      <h3 className="text-xl font-bold text-gray-900 mb-2 hover:text-green-600 cursor-pointer transition-colors">
                        Community-Based Environmental Education: Impact Assessment in Rural Cameroon
                      </h3>
                      
                      <p className="text-gray-600 mb-4 leading-relaxed">
                        This study evaluates the effectiveness of community-based environmental education programs 
                        in rural Cameroon, measuring knowledge retention, behavioral changes, and long-term 
                        environmental conservation outcomes.
                      </p>
                      
                      <div className="flex items-center space-x-4 mb-4">
                        <div className="flex items-center space-x-2">
                          <Avatar className="h-8 w-8">
                            <AvatarFallback className="bg-green-100 text-green-800 text-xs">PT</AvatarFallback>
                          </Avatar>
                          <span className="text-sm text-gray-600">Paul Tabi</span>
                        </div>
                        <div className="text-gray-400">•</div>
                        <div className="flex items-center space-x-1 text-sm text-gray-500">
                          <Eye className="h-4 w-4" />
                          <span>987 views</span>
                        </div>
                        <div className="flex items-center space-x-1 text-sm text-gray-500">
                          <Download className="h-4 w-4" />
                          <span>156 downloads</span>
                        </div>
                      </div>
                      
                      <div className="flex flex-wrap gap-2">
                        <Badge variant="secondary" className="text-xs">Environmental Education</Badge>
                        <Badge variant="secondary" className="text-xs">Community Development</Badge>
                        <Badge variant="secondary" className="text-xs">Rural Areas</Badge>
                        <Badge variant="secondary" className="text-xs">Impact Assessment</Badge>
                      </div>
                    </div>
                    
                    <div className="ml-6 flex flex-col space-y-2">
                      <Button size="sm" className="bg-green-600 hover:bg-green-700">
                        <Download className="h-4 w-4 mr-2" />
                        Download
                      </Button>
                      <Button size="sm" variant="outline">
                        <Eye className="h-4 w-4 mr-2" />
                        Preview
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Pagination */}
            <div className="flex items-center justify-between mt-12">
              <div className="text-sm text-gray-600">
                Showing 1-10 of 25 results
              </div>
              <div className="flex items-center space-x-2">
                <Button variant="outline" size="sm">Previous</Button>
                <Button variant="outline" size="sm" className="bg-green-600 text-white">1</Button>
                <Button variant="outline" size="sm">2</Button>
                <Button variant="outline" size="sm">3</Button>
                <Button variant="outline" size="sm">Next</Button>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
